{"timestamp":"2025-07-24T00:11:15.778395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:11:15.782233Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.782355Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.782432Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.784942Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785084Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785184Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785246Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785292Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785345Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785402Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785455Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785519Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785565Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785617Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785671Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785715Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785762Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785805Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785849Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785893Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785941Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.785985Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786028Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786070Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786112Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786155Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786197Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786241Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786284Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786411Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786476Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.786524Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.788440Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.788517Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:15.788565Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.430981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:11:46.439250Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439416Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439507Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439568Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439614Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439659Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439722Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439797Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439859Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439910Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.439956Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440004Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440050Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440107Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440159Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440212Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440269Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440318Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440362Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440405Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440453Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440497Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440539Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440621Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440664Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440707Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440749Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440790Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440833Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440875Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440915Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440957Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.440998Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.441038Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:11:46.441078Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.720049","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:27:41.723159Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.723259Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.723314Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.725264Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.725739Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.725871Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.725956Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726005Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726058Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726113Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726166Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726219Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726266Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726318Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726385Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726428Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726510Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726554Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726597Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726639Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726681Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726722Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726763Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726805Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726845Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726885Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726924Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.726964Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.727004Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.727044Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.727083Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.727123Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.728879Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.728964Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:27:41.729014Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.204520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:43:37.207848Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210224Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210421Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210499Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210566Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210630Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210685Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210729Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210781Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210834Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210915Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.210966Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211007Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211055Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211117Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211186Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211256Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211301Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211344Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211386Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211427Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211468Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211509Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211550Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211611Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211659Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211700Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211742Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211784Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211825Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211866Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.211906Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.213567Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.213637Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:43:37.213682Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.937153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:44:07.940606Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.940707Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943352Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943519Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943635Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943701Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943757Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943800Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943852Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943904Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.943989Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944052Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944195Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944249Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944292Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944335Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944377Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944418Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944461Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944502Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944543Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944583Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944623Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944666Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944706Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944746Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944786Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944826Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944865Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944916Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.944957Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.946668Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.946735Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T00:44:07.946808Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.480532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:00:02.483491Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.483633Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.483691Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.483953Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.484107Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486107Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486565Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486692Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486847Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486912Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.486961Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487006Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487051Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487094Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487138Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487181Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.487227Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.488913Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.488979Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489089Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489132Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489173Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489215Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489258Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489299Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489341Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489419Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489463Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489506Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489552Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489595Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489638Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489680Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:00:02.489723Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.760570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:15:57.767884Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768002Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768064Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768130Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768188Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768233Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768280Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768326Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768377Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768437Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768485Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768539Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768594Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768652Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768706Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768768Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768812Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768855Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768899Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.768969Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769040Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769127Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769175Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769218Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769266Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769318Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769364Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769405Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769447Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769496Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769538Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769584Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769626Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769682Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:15:57.769737Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.475432","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:16:28.479798Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481100Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481216Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481318Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481378Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481427Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481473Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481519Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481565Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481610Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481654Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481705Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481760Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481812Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481897Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.481950Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482017Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482092Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482143Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482188Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482232Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482275Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482317Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482358Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482399Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482440Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482480Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482521Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482561Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482601Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482642Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482682Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482722Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482763Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:16:28.482804Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.902811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:32:22.908997Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909097Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909159Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909218Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909265Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909311Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909363Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909417Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909494Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909536Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909577Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909626Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909677Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909729Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909778Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909827Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909876Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.909952Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910001Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910043Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910085Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910126Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910168Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910209Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910254Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910295Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910336Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910377Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910418Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910459Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910501Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910542Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910583Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910626Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:22.910667Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.640113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:32:53.643243Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.643343Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.644428Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.645971Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646076Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646144Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646199Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646244Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646290Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646336Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646386Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646438Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646480Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646530Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646613Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646664Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646717Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646761Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646805Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646848Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646889Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646931Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.646971Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647011Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647051Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647091Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647131Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647171Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647210Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647250Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647290Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.647330Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.649097Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.649174Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:32:53.649222Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.037319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:48:49.041498Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.041636Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045098Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045348Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045446Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045516Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045575Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045625Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045673Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045722Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045782Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045835Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045878Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045930Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.045985Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046029Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046085Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046132Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046176Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046220Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046263Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046305Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046347Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046388Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046431Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046517Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046559Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046601Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046643Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046684Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046727Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.046769Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.048744Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.048825Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T01:48:49.048876Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.505268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:04:44.511917Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512038Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512095Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512149Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512205Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512249Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512292Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512345Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512400Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512444Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512486Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512531Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512601Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512685Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512743Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512801Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512855Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512900Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.512944Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513024Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513090Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513165Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513217Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513259Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513302Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513345Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513387Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513429Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513470Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513513Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513554Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513599Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513641Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513684Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:04:44.513725Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.180056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:05:15.183179Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.183281Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.183984Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.185733Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.185973Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186063Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186123Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186172Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186222Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186276Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186338Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186393Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186480Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186529Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186582Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186625Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186672Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186719Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186764Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186806Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186849Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186893Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186934Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.186988Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187042Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187104Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187153Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187210Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187277Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187359Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187424Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.187482Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.189991Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.190087Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:05:15.190140Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.048778","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:21:01.053199Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.053330Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.053408Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.054129Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.055136Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.055913Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056238Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056302Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056358Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056422Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056490Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056543Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056587Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056635Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056686Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056735Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056787Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056828Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056869Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056912Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056952Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.056992Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057032Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057088Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057130Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057172Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057216Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057258Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057299Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057340Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057382Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.057422Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.059024Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.059106Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:21:01.059154Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.396954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:36:56.406045Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406239Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406339Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406426Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406490Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406541Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406605Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406707Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406790Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406852Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406900Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.406954Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407010Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407080Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407148Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407230Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407292Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407379Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407444Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407501Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407548Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407595Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407638Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407695Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407758Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407808Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407863Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407918Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.407968Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408061Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408108Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408152Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408196Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408250Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:36:56.408304Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.189966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:37:27.195744Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.195949Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196046Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196117Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196190Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196243Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196293Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196342Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196399Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196457Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196523Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196583Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196633Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196689Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196747Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196803Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196871Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196930Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.196976Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197022Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197067Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197111Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197154Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197220Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197262Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197305Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197347Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197390Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197431Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197481Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197524Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197565Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197608Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197650Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:37:27.197692Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.620795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:53:22.629431Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629530Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629595Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629655Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629716Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629767Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629820Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629880Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629940Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.629993Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630035Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630078Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630121Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630171Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630221Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630270Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630322Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630388Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630429Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630470Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630511Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630564Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630607Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630648Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630689Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630731Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630772Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630813Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630853Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630894Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630935Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.630974Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.631015Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.631057Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T02:53:22.631098Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.012104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:09:18.017382Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.017980Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018492Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018578Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018662Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018735Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018787Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018836Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018885Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.018934Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019003Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019048Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019092Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019146Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019199Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019251Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019302Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019345Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019417Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019471Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019515Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019557Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019600Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019642Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019685Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019731Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019776Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019817Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019858Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.019925Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.020049Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.020118Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.020167Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.020230Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:18.020300Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.660739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:09:48.668734Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.668896Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669009Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669080Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669167Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669217Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669272Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669330Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669382Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669439Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669486Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669531Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669574Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669626Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669681Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669737Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669788Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669831Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669878Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669933Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.669975Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670017Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670057Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670098Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670139Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670181Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670221Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670262Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670302Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670344Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670384Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670425Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670466Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670509Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:09:48.670571Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.254177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:25:43.257565Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.257700Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.258089Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.258164Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.260948Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261136Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261200Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261263Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261367Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261427Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261474Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261520Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261567Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261611Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261656Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261699Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.261740Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263593Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263673Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263723Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263770Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263816Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263859Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263901Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263944Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.263985Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264027Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264097Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264139Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264180Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264221Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264280Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264323Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264367Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:25:43.264410Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.698903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:41:26.705270Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705413Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705489Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705553Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705605Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705653Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705699Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705746Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705799Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705856Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705901Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.705975Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706051Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706114Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706171Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706228Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706290Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706344Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706391Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706448Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706493Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706564Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706609Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706652Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706696Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706739Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706781Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706824Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706868Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706911Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706954Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.706998Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.707042Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.707089Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:26.707134Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.300395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:41:57.303685Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.303786Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.303844Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.304595Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.306503Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.306790Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.306880Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.306962Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307019Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307087Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307160Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307217Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307263Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307315Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307368Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307439Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307484Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307531Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307580Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307626Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307670Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307714Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307758Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307802Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307845Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307888Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307930Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.307975Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.308016Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.308059Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.308104Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.308146Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.310095Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.310171Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T03:41:57.310218Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.284715","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:02:30.289678Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.289838Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.289922Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291553Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291673Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291772Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291856Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291923Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.291991Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292100Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292178Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292272Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292339Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292416Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292517Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292596Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292680Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292743Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292815Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292878Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.292951Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293024Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293086Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293150Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293213Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293275Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293338Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293401Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293469Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.293577Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.295565Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.295787Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.298398Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.298514Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:02:30.298590Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.944867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:03:00.950592Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.950723Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.950838Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.950901Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.950953Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951003Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951058Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951113Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951158Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951203Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951246Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951289Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951331Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951380Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951446Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951539Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951608Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951656Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951711Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951756Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951809Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951860Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951904Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951950Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.951992Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952033Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952073Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952115Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952155Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952196Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952237Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952278Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952340Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952382Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:03:00.952423Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.283869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:18:55.286854Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.286949Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.287823Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289190Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289437Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289586Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289648Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289696Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289746Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289792Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289850Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289905Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.289951Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290004Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290056Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290099Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290145Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290187Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290231Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290273Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290317Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290360Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290436Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290499Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290543Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290588Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290661Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290704Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290747Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290816Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290887Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.290935Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.292666Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.292737Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:18:55.292789Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.726709","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:34:50.731385Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.731900Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733308Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733562Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733747Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733838Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733895Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733944Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.733992Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734039Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734084Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734129Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734175Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734226Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734278Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734329Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734381Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734426Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734469Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734538Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734580Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734621Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734665Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734707Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734748Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734788Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734832Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734873Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734915Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734957Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.734998Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.735039Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.735081Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.735124Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:34:50.735165Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.317822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:35:21.323189Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323377Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323458Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323511Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323560Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323605Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323658Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323729Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323809Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323861Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323922Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.323971Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324016Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324106Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324160Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324213Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324272Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324315Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324358Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324400Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324442Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324484Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324524Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324564Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324605Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324645Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324685Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324725Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324765Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324808Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324850Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324890Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324938Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.324982Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:35:21.325022Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.611419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:51:16.614743Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.614852Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.614925Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.615913Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617318Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617488Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617580Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617668Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617716Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617763Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617816Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617882Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617925Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.617975Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618033Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618085Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618139Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618182Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618226Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618269Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618312Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618352Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618396Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618436Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618477Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618518Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618558Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618599Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618639Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618679Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618720Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.618761Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.620961Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.621042Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T04:51:16.621094Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.038919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:07:13.042123Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.042223Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.042276Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.043109Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.043498Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.044524Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.044748Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.044953Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045043Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045105Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045164Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045221Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045268Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045321Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045376Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045422Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045488Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045534Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045578Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045621Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045664Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045711Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045753Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045795Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045839Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045882Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045924Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.045966Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.046008Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.046050Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.046113Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.046154Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.047917Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.048011Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:13.048063Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.742806","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:07:43.745518Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.745779Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.745935Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748215Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748378Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748455Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748509Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748555Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748601Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748644Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748688Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748732Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748775Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748818Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748861Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748905Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.748946Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750749Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750819Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750864Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750908Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750950Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.750992Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751033Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751104Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751145Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751195Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751237Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751278Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751318Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751359Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751399Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751441Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751483Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:07:43.751525Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.117056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:23:38.123661Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.124998Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125135Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125225Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125295Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125346Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125394Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125440Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125485Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125532Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125576Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125623Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125666Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125717Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125773Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125825Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125875Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125943Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.125987Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126031Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126072Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126113Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126196Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126238Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126285Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126326Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126396Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126456Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126505Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126551Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126596Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126641Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126686Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:23:38.126730Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.277595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:29:31.281045Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.281323Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.281568Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.281650Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.281709Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.283850Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.284953Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285212Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285370Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285428Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285484Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285567Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285622Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285676Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285729Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285780Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.285843Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288044Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288125Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288185Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288240Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288312Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288385Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288437Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288489Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288541Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288611Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288664Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288716Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288767Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288845Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288912Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.288964Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.289017Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:29:31.289068Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.049700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:30:02.054203Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055008Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055457Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055670Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055760Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055839Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055894Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.055971Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056037Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056095Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056154Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056225Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056271Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056323Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056377Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056443Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056495Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056537Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056580Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056622Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056664Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056706Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056747Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056788Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056830Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056872Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056913Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.056953Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057002Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057042Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057083Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057124Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057164Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057207Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:30:02.057275Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.791114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:45:55.796794Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.797089Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.797547Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.798596Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.798891Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799010Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799090Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799170Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799232Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799283Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799332Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799380Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799466Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799568Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799633Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799695Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799772Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799830Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799879Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799927Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.799973Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800049Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800120Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800187Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800236Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800280Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800329Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800375Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800471Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800524Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800592Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800650Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800701Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800755Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T05:45:55.800805Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.691410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:01:37.694677Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.694780Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.694855Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697267Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697471Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697553Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697615Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697662Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697708Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697760Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697850Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697905Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.697951Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698005Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698084Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698145Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698200Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698247Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698293Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698339Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698386Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698432Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698501Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698544Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698585Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698628Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698672Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698716Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698758Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698799Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698841Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.698884Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.700780Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.700861Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:01:37.700924Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.450654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:02:08.465434Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.466710Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.469998Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.471491Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.471726Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.471939Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472106Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472203Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472284Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472360Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472435Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472526Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472634Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472732Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472793Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472881Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.472971Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473017Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473063Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473108Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473152Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473197Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473240Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473285Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473329Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473373Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473417Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473461Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473505Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473557Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473600Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473643Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473687Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473732Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:02:08.473775Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.871552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:18:03.879266Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879352Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879405Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879460Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879534Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879603Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879655Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879706Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879764Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879840Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879884Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.879935Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880005Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880091Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880153Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880211Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880281Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880327Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880373Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880417Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880461Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880513Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880556Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880597Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880639Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880682Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880725Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880768Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880830Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880887Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880935Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.880981Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.881025Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.881069Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:18:03.881112Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.441931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:33:58.445629Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.445746Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.446544Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448350Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448554Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448736Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448802Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448849Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448896Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.448942Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449000Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449054Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449099Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449163Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449218Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449264Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449313Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449360Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449405Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449448Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449490Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449533Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449575Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449617Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449660Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449702Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449744Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449787Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449829Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449871Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449914Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.449957Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.451637Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.451740Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:33:58.451786Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.150584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:34:29.155245Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156571Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156737Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156829Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156892Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156941Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.156997Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157116Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157160Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157202Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157245Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157286Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157344Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157395Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157439Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157480Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157522Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157563Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157613Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157655Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157699Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157740Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157781Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157823Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157865Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157929Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.157971Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158013Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158060Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158102Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158144Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158187Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158230Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:34:29.158272Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.633908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:50:24.639409Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.641074Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.642108Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643019Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643158Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643284Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643399Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643487Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643575Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643663Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643763Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643866Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.643953Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.644064Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.644336Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.644671Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.644802Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.644934Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645111Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645305Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645408Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645492Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645616Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645743Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.645888Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646006Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646187Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646401Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646508Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646648Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646750Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.646888Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.650135Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.650280Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T06:50:24.650385Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.993326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:06:19.997700Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.998985Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999159Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999236Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999303Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999362Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999409Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999455Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999506Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999560Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999609Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999655Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999700Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999776Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999830Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999880Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999931Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:19.999973Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000015Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000056Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000098Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000139Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000180Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000221Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000261Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000302Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000343Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000383Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000423Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000476Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000517Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000558Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000599Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000641Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:20.000682Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.659858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:06:50.663262Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.664285Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.664630Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.665306Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.665795Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666049Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666123Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666200Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666258Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666315Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666369Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666422Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666466Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666517Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666569Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666612Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666655Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666697Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666741Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666783Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666825Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666866Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666908Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666950Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.666991Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667031Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667072Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667112Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667156Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667196Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667236Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.667280Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.669007Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.669110Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:06:50.669170Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.058530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:22:46.061800Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.061929Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.063304Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064308Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064504Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064579Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064642Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064704Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064766Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064814Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064874Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064928Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.064973Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065022Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065073Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065127Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065180Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065222Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065264Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065305Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065346Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065387Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065428Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065468Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065509Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065553Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065597Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065638Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065680Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065745Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065838Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.065886Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.067480Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.067547Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:22:46.067597Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.738997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:30:25.744336Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.744889Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.744990Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745089Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745157Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745215Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745280Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745336Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745383Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745428Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745472Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745516Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745560Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745612Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745664Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745714Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745765Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745807Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745848Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745889Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745930Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.745971Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746015Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746055Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746131Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746172Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746212Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746253Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746294Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746335Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746376Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746416Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746457Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746498Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:25.746538Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.439131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:30:56.442623Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.443462Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.444367Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.444730Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445318Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445472Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445562Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445614Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445662Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445709Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445765Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445819Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445863Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445915Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.445969Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446011Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446055Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446097Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446163Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446206Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446247Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446305Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446346Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446387Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446428Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446468Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446509Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446551Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446591Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446631Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446675Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.446718Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.448445Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.448516Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:30:56.448564Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.903401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:46:51.906701Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.906802Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.906857Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909531Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909648Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909708Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909761Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909808Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909855Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909900Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.909970Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910073Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910147Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910207Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910265Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910323Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910390Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910441Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910487Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910534Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910581Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910628Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910676Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910718Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910765Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910811Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910856Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910901Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910945Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.910988Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.911030Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.911085Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.913158Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.913252Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T07:46:51.913315Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.193613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:02:47.196808Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.196911Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.196964Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.198866Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199495Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199683Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199750Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199812Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199861Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.199911Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200023Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200093Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200143Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200195Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200251Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200296Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200340Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200383Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200425Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200466Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200507Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200548Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200590Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200630Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200671Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200712Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200756Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200805Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200845Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200886Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200927Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.200994Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.202646Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.202719Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:02:47.202780Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.907858","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:03:17.915558Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.915728Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.915804Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.915854Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.915901Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.915947Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916012Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916091Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916146Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916192Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916239Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916284Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916329Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916384Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916437Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916484Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916526Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916568Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916612Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916655Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916705Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916745Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916786Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916827Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916868Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916910Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916949Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.916990Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917060Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917105Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917148Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917192Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917233Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917274Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:03:17.917314Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.531473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:19:13.540590Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.540698Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.540756Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.540822Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.540915Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.540974Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541034Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541094Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541142Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541217Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541302Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541364Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541418Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541478Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541540Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541600Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541651Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541699Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541758Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541817Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541882Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.541947Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542026Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542071Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542116Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542161Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542206Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542251Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542295Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542352Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542413Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542469Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542516Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542573Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:13.542633Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.332590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:19:44.336514Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.336642Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.336723Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.339535Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.339695Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.339928Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340031Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340103Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340160Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340218Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340286Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340340Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340385Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340436Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340491Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340546Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340645Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340694Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340742Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340787Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340831Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340882Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340929Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.340975Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341019Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341062Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341109Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341152Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341194Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341237Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341281Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.341327Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.343332Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.343402Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:19:44.343449Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.088051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:22:07.094102Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.094254Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.095339Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.097984Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098120Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098189Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098250Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098303Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098372Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098453Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098529Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098589Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098637Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098691Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098747Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098793Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098842Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098900Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.098963Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099012Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099058Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099106Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099153Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099199Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099243Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099288Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099361Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099435Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099479Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099548Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099602Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.099651Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.101994Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.102088Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:07.102145Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.812939","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:22:37.825303Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825407Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825463Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825563Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825636Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825697Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825745Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825798Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825860Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825917Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.825961Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826021Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826084Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826141Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826196Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826251Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826306Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826349Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826394Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826439Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826483Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826526Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826580Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826624Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826680Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826729Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826779Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826826Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826871Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826915Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.826965Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.827009Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.827052Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.827123Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:22:37.827167Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.484547","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:23:08.489833Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.489956Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492596Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492715Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492783Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492851Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492911Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.492958Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493013Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493084Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493139Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493194Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493243Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493296Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493352Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493405Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493461Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493506Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493550Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493593Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493637Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493680Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493722Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493763Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493807Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493849Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493935Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.493977Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.494019Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.494062Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.494112Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.494155Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.496024Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.496093Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:08.496138Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.160739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:23:39.163890Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.164068Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167049Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167166Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167247Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167338Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167399Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167446Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167493Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167544Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167610Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167666Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167712Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167763Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167816Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167860Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167906Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167950Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.167994Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168038Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168103Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168146Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168187Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168228Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168273Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168315Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168356Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168397Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168438Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168479Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168533Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.168582Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.170332Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.170398Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:23:39.170462Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.804768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:24:09.810913Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.811127Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.811247Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.814500Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.814840Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815003Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815078Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815129Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815177Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815226Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815280Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815383Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815432Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815484Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815565Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815648Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815721Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815783Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.815942Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816012Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816058Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816101Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816160Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816219Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816264Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816312Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816357Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816400Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816462Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816534Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816587Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.816637Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.818910Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.818985Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:09.819033Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.495690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:24:40.500580Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502214Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502308Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502359Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502417Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502473Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502525Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502579Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502651Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502695Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502737Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502780Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502824Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502874Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502924Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.502970Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503013Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503057Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503099Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503140Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503181Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503226Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503267Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503308Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503349Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503390Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503431Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503473Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503516Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503561Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503603Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503644Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503687Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503730Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:24:40.503772Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.193092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:25:11.197308Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.197486Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.198815Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.199933Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200132Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200224Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200302Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200354Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200403Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200450Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200505Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200558Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200603Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200655Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200707Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200752Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200795Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200838Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200880Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.200943Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201001Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201047Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201093Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201137Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201182Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201225Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201267Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201309Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201350Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201392Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201437Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.201510Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.203229Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.203302Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:11.203349Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.820359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:25:41.828730Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.828887Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.828974Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831252Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831449Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831521Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831580Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831626Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831672Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831718Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831777Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831833Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831877Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831929Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.831985Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832028Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832073Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832118Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832161Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832204Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832246Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832290Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832333Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832386Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832430Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832502Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832545Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832587Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832632Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832675Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832724Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.832768Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.834481Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.834548Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:25:41.834615Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.437706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:26:12.441648Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.441790Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.442448Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.443473Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.444501Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.444701Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.444957Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445114Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445175Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445226Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445287Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445380Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445492Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445592Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445707Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445791Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445889Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.445992Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446134Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446203Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446279Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446347Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446412Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446467Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446523Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446568Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446612Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446655Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446697Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446740Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446783Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.446826Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.449091Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.449301Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:12.449349Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.099814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:26:43.105395Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.107141Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.107262Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.107568Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.107669Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.107907Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.108009Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.110370Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.110764Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.110970Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111126Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111193Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111293Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111367Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111433Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111488Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.111541Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113552Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113629Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113683Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113731Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113776Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113821Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113866Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113910Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113954Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.113996Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114039Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114082Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114128Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114170Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114214Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114258Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114308Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:26:43.114355Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.944742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:27:13.949291Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.950814Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951000Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951159Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951242Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951306Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951379Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951427Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951474Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951520Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951565Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951616Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951674Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951727Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951781Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951828Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951876Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951939Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.951990Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952039Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952101Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952145Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952189Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952233Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952277Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952319Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952361Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952405Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952449Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952491Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952535Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952582Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952627Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952672Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:13.952716Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.690216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:27:44.693618Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.693772Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.693849Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.694064Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.694214Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.696639Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.696839Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.696906Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.696957Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697004Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697050Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697094Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697142Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697185Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697230Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697275Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.697320Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699255Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699333Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699385Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699431Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699476Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699519Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699563Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699610Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699653Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699715Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699765Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699808Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699875Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699917Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.699959Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.700022Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.700089Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:27:44.700137Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.324780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:28:15.328366Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.328475Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.328553Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.330601Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.330948Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331197Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331276Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331329Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331376Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331423Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331476Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331545Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331592Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331643Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331703Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331749Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331796Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331839Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331883Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331926Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.331968Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332012Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332052Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332119Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332160Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332201Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332242Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332282Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332323Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332365Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332406Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.332447Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.334195Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.334265Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:15.334310Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.073385","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:28:46.077233Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.077365Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.079921Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080195Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080314Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080385Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080444Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080505Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080552Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080607Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080682Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080738Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080784Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080837Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080891Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.080935Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081008Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081050Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081093Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081136Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081178Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081254Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081343Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081414Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081466Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081512Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081558Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081604Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081655Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081702Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081744Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.081786Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.083974Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.084136Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:28:46.084207Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.750982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:29:16.759187Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759351Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759435Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759492Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759551Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759610Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759662Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759718Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759764Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759808Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759908Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759953Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.759997Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760055Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760110Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760154Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760199Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760242Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760288Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760331Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760372Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760413Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760467Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760511Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760552Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760596Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760636Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760678Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760742Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760811Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760860Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760908Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.760954Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.761003Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:16.761046Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.414839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:29:47.418307Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.418518Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.418636Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421475Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421619Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421685Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421741Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421803Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421872Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421922Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.421980Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422033Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422077Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422126Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422178Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422221Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422269Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422314Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422359Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422401Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422442Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422484Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422526Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422580Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422621Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422663Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422706Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422748Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422789Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422830Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422871Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.422912Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.424686Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.424757Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:29:47.424839Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.091270","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:30:18.095180Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.097720Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.097873Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.097980Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098046Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098111Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098173Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098228Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098281Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098336Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098387Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098438Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098482Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098531Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098582Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098633Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098708Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098773Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098824Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098867Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098910Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098953Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.098996Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099038Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099079Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099120Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099160Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099227Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099267Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099308Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099349Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.099389Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.101284Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.101364Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:18.101415Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.793901","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:30:48.796894Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.797038Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.797096Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.797290Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.797429Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.799935Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800115Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800183Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800232Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800279Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800325Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800370Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800412Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800454Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800497Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800540Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.800581Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802215Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802283Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802330Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802373Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802443Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802485Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802526Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802568Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802609Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802650Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802691Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802732Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802775Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802816Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802860Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802902Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802943Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:30:48.802984Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.516283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:31:19.522334Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522454Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522509Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522556Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522608Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522662Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522706Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522751Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522796Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522840Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522882Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522925Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.522968Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523017Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523092Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523216Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523302Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523358Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523427Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523498Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523550Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523598Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523644Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523689Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523733Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523778Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523824Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523868Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523912Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.523957Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.524002Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.524045Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.524087Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.524135Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:19.524192Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.244283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:31:50.248619Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.250341Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251337Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251553Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251650Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251724Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251783Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251830Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251904Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.251952Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252006Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252059Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252150Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252201Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252244Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252290Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252333Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252402Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252466Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252522Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252573Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252626Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252693Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252754Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252804Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252852Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252899Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252945Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.252990Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.253036Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.253083Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.255538Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.255668Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:31:50.255747Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.225542","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:32:21.231720Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.231855Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.231943Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.231992Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232048Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232104Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232162Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232223Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232277Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232322Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232365Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232418Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232469Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232519Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232569Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232613Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232662Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232717Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232758Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232803Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232844Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232885Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232926Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.232967Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233057Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233117Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233162Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233205Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233247Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233292Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233333Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233375Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233473Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233549Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:21.233602Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.943752","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:32:51.946447Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.946643Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.946788Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.947094Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.947319Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.947446Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.947554Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.949053Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.950874Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.950950Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951021Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951079Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951129Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951178Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951234Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951290Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.951336Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.953221Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.953857Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954046Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954215Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954273Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954320Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954366Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954411Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954480Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954523Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954566Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954609Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954650Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954691Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954733Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954775Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954822Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-24T08:32:51.954863Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
