{"timestamp":"2025-07-19T21:47:54.415864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:47:54.495732","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:48:34.835516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:48:35.094917","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:49:06.780721","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:49:06.857356","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:49:39.108934","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:49:39.193196","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:50:09.679811","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:50:09.702764","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:50:40.494279","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:50:40.510512","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:51:11.339208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:51:11.355183","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:51:41.995815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:51:42.018789","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:52:12.563325","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:52:12.575692","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:52:43.229240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:52:43.249898","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:53:13.852799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:53:13.869373","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:53:44.803652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:53:44.927063","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:54:15.792581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:54:15.807528","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:54:46.463823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:54:46.476660","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:55:17.189492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:55:17.206592","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:55:47.893619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:55:47.916873","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:56:18.630554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:56:18.650703","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:56:49.273992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:56:49.291265","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:57:19.980334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:57:20.003721","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:57:50.594080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:57:50.609548","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:58:21.154291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:58:21.171894","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:58:51.794543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:58:51.808517","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:59:22.379132","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:59:22.398733","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T21:59:53.029427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:59:53.044545","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:00:23.685200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:00:23.702977","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:00:54.433997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:00:54.454323","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:01:25.248456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:01:25.266923","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:01:56.072265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:01:56.093617","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:02:26.857054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:02:26.878979","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:02:57.604034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:02:57.626032","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:03:28.400888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:03:28.423797","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:03:58.948398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:03:58.963605","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:04:29.682452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:04:29.702696","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:05:00.264611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:05:00.284130","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:05:30.950608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:05:30.991007","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:06:01.681757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:06:01.699987","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:06:32.413615","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:06:32.434392","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:07:03.859401","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:07:03.904655","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:22:54.008204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:22:54.157680","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:23:24.945257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:23:24.960474","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:39:19.386058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:39:19.403303","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:55:14.691904","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:55:14.719086","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T22:55:45.643077","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T22:55:45.662994","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T23:11:40.083104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T23:11:40.105405","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T23:27:35.424119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T23:27:35.443356","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T23:28:06.148495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T23:28:06.159957","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T23:44:01.462222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T23:44:01.478495","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T23:59:57.053743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T23:59:57.083980","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
