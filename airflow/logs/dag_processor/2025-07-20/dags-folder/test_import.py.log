{"timestamp":"2025-07-20T00:00:27.818396","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:00:27.835821Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836026Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836100Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836161Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836217Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836295Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836378Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836458Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836527Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836591Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836687Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836772Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836844Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836909Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.836973Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837043Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837090Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837153Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837209Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837266Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837314Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837361Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837417Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837462Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837507Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837566Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837620Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837663Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837715Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837831Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837884Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837928Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.837971Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.838032Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:00:27.838083Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.719095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:08:25.728455Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.728837Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.728979Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.729334Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.732216Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.732776Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.732880Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.732943Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.736887Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.736973Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.739870Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740080Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740187Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740267Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740343Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740410Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740474Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740540Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740607Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740657Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740714Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740769Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740862Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.740977Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741028Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741079Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741156Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741203Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741267Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741319Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741367Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741411Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741455Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741501Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:08:25.741547Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.438081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:24:20.446741Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.446954Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.447198Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.447397Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.448560Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.451638Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.451861Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.451963Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452068Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452140Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452204Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452267Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452333Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452400Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452454Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452541Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452702Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452788Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452842Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452885Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452927Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.452968Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453010Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453051Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453091Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453202Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453430Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453572Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453633Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453680Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453725Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.453773Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.460148Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.460305Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:20.460375Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.183655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:24:51.188496Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.188672Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.189056Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.189278Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.189391Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.189479Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.189526Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.191905Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192066Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192153Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192239Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192289Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192337Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192382Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192433Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192482Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.192527Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.194822Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.194926Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.194985Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195033Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195089Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195135Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195179Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195224Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195268Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195310Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195384Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195456Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195540Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195602Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195657Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195714Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195761Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:24:51.195804Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.021570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:40:37.028567Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.028781Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.029253Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.030595Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.030880Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.031654Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032609Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032681Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032737Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032796Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032859Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032923Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.032974Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.033029Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.033082Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.033155Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.033218Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.037017Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.037225Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.037366Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.037430Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.039002Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040008Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040598Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040664Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040715Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040777Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040833Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040882Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040926Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.040979Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.041026Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.041068Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.041112Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:40:37.041200Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.444783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:56:33.452527Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.453023Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.455037Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.456962Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457140Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457228Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457313Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457417Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457527Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457593Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457647Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457704Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457757Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457818Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457881Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.457964Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458066Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458171Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458274Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458388Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458441Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458489Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458534Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458579Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458623Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458666Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458710Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458800Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458842Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458883Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458938Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.458979Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.462622Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.462947Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:56:33.463233Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.125567","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:57:04.132602Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.132801Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.136105Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139056Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139397Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139509Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139577Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139625Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139679Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139736Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139788Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139855Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.139942Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140009Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140087Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140152Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140232Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140325Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140396Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140462Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140521Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140621Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140677Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140725Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140771Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140839Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140895Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.140943Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.141006Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.141071Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.141124Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.141183Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.144633Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.145069Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T00:57:04.145149Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.361431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:12:58.371999Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372164Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372242Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372310Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372373Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372449Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372542Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372613Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372684Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372740Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372789Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372841Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372896Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.372948Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373074Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373151Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373219Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373303Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373365Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373415Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373459Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373502Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373545Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373601Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373665Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373736Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373785Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373835Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373902Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.373985Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.374037Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.374079Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.374130Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.374190Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:12:58.374243Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.481314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:28:45.493644Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.493818Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.493914Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.493978Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494030Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494079Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494134Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494200Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494313Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494394Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494489Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494565Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494626Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494683Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494751Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494826Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494911Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.494981Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495052Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495127Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495183Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495234Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495281Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495329Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495392Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495456Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495525Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495592Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495667Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495806Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.495995Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.496052Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.496916Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.497917Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:28:45.499084Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.166532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:29:16.179445Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.179713Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.179856Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.179971Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180079Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180173Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180249Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180328Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180404Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180481Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180540Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180605Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180672Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180742Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180814Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180894Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.180943Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181025Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181103Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181161Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181216Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181284Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181345Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181407Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181528Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181605Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.181692Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.182347Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184577Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184676Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184753Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184810Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184913Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.184966Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:29:16.185019Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.838717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:45:09.852202Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.853771Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.854209Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.854436Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.854858Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.855376Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.856425Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.857567Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.858686Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.859687Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.860259Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.860741Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.861582Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.862009Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.862267Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.863528Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.863877Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.873765Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.873950Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.874128Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.875270Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.875559Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.875695Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.876024Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.876154Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.876828Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877007Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877210Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877282Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877368Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877450Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877523Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877594Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877680Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T01:45:09.877770Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.573271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:01:04.581176Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.582152Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.583065Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586053Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586293Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586402Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586516Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586615Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586719Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586816Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.586916Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587027Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587131Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587238Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587348Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587481Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587595Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587706Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.587807Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588020Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588115Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588246Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588386Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588480Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588571Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588665Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588762Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588857Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.588955Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.589061Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.589151Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.589238Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.594914Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.595204Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:04.596491Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.099031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:01:35.103509Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.103673Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.106163Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.106814Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.106899Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.106957Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107028Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107121Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107207Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107270Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107329Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107375Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107468Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.107519Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.108211Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.109843Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115517Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115677Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115743Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115792Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115838Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115883Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115926Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.115969Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116010Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116053Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116095Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116136Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116176Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116217Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116257Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116298Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116338Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116380Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:01:35.116423Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.513599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:17:30.521336Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.521795Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.522277Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.522506Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.523185Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525127Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525311Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525383Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525430Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525474Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525522Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525570Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525631Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525688Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525733Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.525781Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.529598Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.529721Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532397Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532502Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532562Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532613Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532678Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532748Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532801Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532855Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532906Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.532964Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533023Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533072Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533151Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533211Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533260Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533322Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:17:30.533383Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.155834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:33:25.162379Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.162995Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.165005Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.165099Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.167059Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.167940Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168101Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168194Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168254Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168300Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168345Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168407Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168482Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168536Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168587Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168637Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.168686Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.171596Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.172319Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.173791Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.173888Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.173976Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174041Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174103Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174157Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174208Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174272Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174337Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174392Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174475Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174527Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174577Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174647Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174698Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:25.174747Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.899299","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:33:55.917454Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.918278Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.919333Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920513Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920621Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920696Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920758Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920831Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920911Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.920988Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921051Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921110Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921183Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921250Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921330Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921407Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921492Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921589Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921663Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921721Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921781Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921834Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921896Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.921990Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922045Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922100Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922156Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922214Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922264Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922315Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922365Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922413Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922476Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922535Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:33:55.922584Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.609729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:49:50.621382Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.621529Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.621961Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.622146Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.625647Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.626073Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.626405Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.626612Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.626785Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.627202Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.627584Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.627764Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.627864Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.628106Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.628213Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.628296Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.628371Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.631874Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.632012Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.632161Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.632852Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.635798Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636114Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636188Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636246Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636295Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636368Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636440Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636488Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636534Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636587Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636634Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636681Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636732Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T02:49:50.636803Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.958321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:05:45.967143Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.969008Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.971468Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.973945Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.983954Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984455Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984534Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984605Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984671Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984735Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984853Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.984936Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985000Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985061Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985127Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985201Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985265Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985354Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985414Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985483Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985527Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985571Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985616Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985659Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985703Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985749Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985793Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985836Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985879Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985931Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.985974Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.986017Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.986060Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.986105Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:05:45.986148Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.737155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:06:16.743481Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.745303Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.745703Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.746234Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.746554Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.754510Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.755365Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.755508Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.755612Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.756297Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758099Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758331Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758401Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758456Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758520Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758583Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758645Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758707Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758763Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758810Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758852Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758895Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758941Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.758984Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759028Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759079Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759122Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759163Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759204Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759258Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759299Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759341Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759382Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759424Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:06:16.759489Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.441158","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:22:11.454248Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454445Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454534Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454605Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454673Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454732Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454812Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454911Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.454978Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455041Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455092Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455154Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455214Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455273Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455343Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455404Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455456Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455521Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455581Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455633Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455678Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455723Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455780Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455826Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455875Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455928Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.455983Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456056Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456144Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456202Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456261Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456307Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456359Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:22:11.456476Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.862911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:38:06.872271Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.872415Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.873061Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.873998Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874107Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874214Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874301Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874373Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874457Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874524Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874591Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874655Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874706Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874768Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874844Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874903Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.874950Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.875017Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.875078Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.875143Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.875207Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.876357Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.876641Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.876933Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.877399Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.877683Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.877989Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.878064Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.878127Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.878187Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.878278Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.878838Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.885257Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.885645Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:06.885735Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.444563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:38:37.453468Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.456338Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.465625Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.465795Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.465867Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.465943Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466021Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466096Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466160Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466235Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466311Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466410Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466471Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466570Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466655Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466789Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466883Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.466966Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467037Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467114Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467168Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467234Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467286Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467356Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467409Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467474Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467538Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467626Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467686Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467735Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467785Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467839Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467890Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.467951Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:38:37.468009Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.863392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:54:32.869636Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.869744Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.869835Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.869892Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.869950Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.870024Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.870150Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.870400Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.870483Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.870770Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.873503Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.874225Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.875111Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.876295Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.878276Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.879306Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881252Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881370Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881443Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881501Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881550Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881599Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881648Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881700Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881755Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881797Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881843Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881888Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881930Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.881970Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.882013Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.882057Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.889157Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.889270Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T03:54:32.889348Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.921003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:04:48.937683Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.940142Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.941746Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.942772Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.947700Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.950555Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.950862Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951147Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951239Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951317Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951416Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951530Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951613Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951679Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951748Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951882Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.951941Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957081Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957289Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957393Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957467Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957554Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957618Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957681Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957742Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957819Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.957931Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958011Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958075Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958140Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958210Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958281Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958503Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.958663Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:04:48.959851Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.622655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:05:19.633151Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.633811Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.639995Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640162Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640239Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640307Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640374Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640435Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640494Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640548Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640603Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640650Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640694Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640746Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.640808Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.641380Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.641928Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.642103Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.642908Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.643251Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.643605Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.643731Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.643833Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.645239Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646172Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646491Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646802Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646862Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646922Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.646970Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.647017Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.647060Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.647104Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.647147Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:05:19.647190Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.077114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:21:15.082667Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.082854Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.083788Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086142Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086241Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086315Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086389Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086460Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086527Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.086624Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.087875Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.087980Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.088085Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.088152Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.088283Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.088351Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.088398Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.092730Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.093117Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094094Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094168Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094224Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094281Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094332Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094379Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094424Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094472Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094544Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094596Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094643Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094705Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094775Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094828Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094875Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:21:15.094921Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.621319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:37:10.640079Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640223Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640279Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640344Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640399Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640450Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640520Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640582Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640659Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640724Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640781Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640829Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640876Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.640981Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641041Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641092Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641167Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641255Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641326Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641419Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641473Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641524Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641582Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641652Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641727Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641774Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641817Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641859Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641901Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641942Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.641984Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.642025Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.642076Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.642141Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:10.642189Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.335451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:37:41.346195Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.350525Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351031Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351151Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351213Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351271Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351329Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351442Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351517Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351579Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351635Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351698Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351758Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351824Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351897Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.351965Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352022Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352088Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352146Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352201Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352253Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352308Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352365Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352412Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352459Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352518Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352575Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352634Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352679Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352752Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352804Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.352851Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.355718Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.355894Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:37:41.355974Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.793957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:53:34.803174Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.804798Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.806854Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.808804Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.809539Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.809892Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.812825Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.813099Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.813346Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.814513Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.814859Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815063Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815144Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815210Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815302Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815387Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.815462Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.820415Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.820802Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.822277Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.822822Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.824187Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.824973Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825420Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825551Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825624Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825688Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825758Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825821Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825882Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.825982Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.826060Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.826205Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.826346Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T04:53:34.826463Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.389968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:09:30.403063Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.403159Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.403230Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404368Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404608Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404681Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404748Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404820Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.404902Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405010Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405082Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405141Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405192Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405263Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405329Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405387Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405442Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405511Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405621Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405719Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405774Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405820Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405865Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.405910Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406008Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406071Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406119Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406168Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406216Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406280Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406350Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406406Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406451Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406505Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:09:30.406573Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.086367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:10:01.094160Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.094625Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.094774Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.094908Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.096061Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.096367Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.096513Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.096840Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.096997Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.097193Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.097281Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.097521Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.097703Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.097787Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.100995Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101180Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101243Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101338Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101395Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101451Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101503Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101568Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101696Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101778Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101842Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101894Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.101981Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.102150Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.102224Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.102271Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.102315Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.102360Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.105182Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.105316Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:10:01.105405Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.591508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:25:54.602490Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.602723Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.602795Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.602879Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.602957Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603027Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603092Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603156Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603236Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603303Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603363Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603443Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603487Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603538Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603603Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603665Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603729Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603797Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.603858Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.605567Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.605762Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.605977Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606069Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606161Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606227Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606286Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606335Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606383Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606439Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606490Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606541Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606588Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606642Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606695Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:25:54.606740Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.274012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:26:25.279503Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.281412Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.290575Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291563Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291659Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291772Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291837Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291908Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.291971Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292044Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292098Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292153Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292211Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292264Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292335Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292388Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292438Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292500Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292553Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292599Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292641Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292685Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292729Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292772Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292814Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292859Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292901Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292942Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.292984Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293026Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293070Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293112Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293153Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293195Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:26:25.293262Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.599391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:42:20.605646Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607467Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607602Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607707Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607805Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607854Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.607928Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.613918Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614100Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614172Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614231Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614280Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614326Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614380Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614455Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614509Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614576Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614656Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614741Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614796Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614855Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614922Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.614973Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615021Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615080Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615148Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615204Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615254Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615354Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615432Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615509Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615576Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615632Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615691Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:42:20.615740Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.551207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:58:00.561327Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.564922Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.566927Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.567173Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.567367Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.567453Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.569862Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.570788Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.570897Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.570948Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571005Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571057Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571152Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571209Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571304Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.571374Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.576985Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.577295Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.577449Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.577581Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.577725Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578001Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578128Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578243Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578348Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578449Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578591Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578677Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578748Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578830Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.578891Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.579431Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.580251Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:00.580453Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.287554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:58:31.301308Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301521Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301618Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301697Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301782Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301859Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.301932Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.302012Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.302104Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.303440Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.305356Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.305544Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.305680Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.305825Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.305987Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306193Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306294Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306471Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306592Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306704Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306833Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.306920Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307043Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307148Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307331Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307484Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307608Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307790Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.307945Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.308817Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.310338Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.310426Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.310474Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.310518Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T05:58:31.310560Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.357243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:20:55.374946Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375154Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375252Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375321Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375387Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375436Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375512Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375587Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375656Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375750Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375808Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375853Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375904Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.375958Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376019Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376078Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376158Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376253Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376316Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376380Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376438Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376484Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376530Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376577Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376624Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376682Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376737Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376782Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376827Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376870Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376940Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.376999Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.377046Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.377092Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:20:55.377135Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.141216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:21:26.147999Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.151753Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.152593Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.157516Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.157831Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.157989Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.158395Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.158531Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.158766Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.159000Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.159157Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.159226Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.161529Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.162472Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.162574Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.162668Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.162775Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167623Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167775Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167850Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167899Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167947Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.167993Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168037Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168082Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168125Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168168Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168243Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168321Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168371Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168435Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168494Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168538Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168609Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:21:26.168652Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.844887","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:37:19.857569Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.857966Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.858072Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.858166Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.858730Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.858924Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.859010Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.859121Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.864059Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.864213Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.865371Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.866372Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.867504Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.867984Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868072Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868151Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868222Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868308Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868373Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868421Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868476Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868521Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868565Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868609Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868652Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868699Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868772Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868815Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868858Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868900Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868944Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.868986Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.869034Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.869078Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:37:19.869121Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.080406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:53:16.086636Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.089177Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.090092Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.090438Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.091218Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.091492Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.093254Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.093616Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094036Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094209Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094277Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094754Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094823Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094872Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094918Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.094966Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.095012Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.099491Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.099616Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.100557Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.102872Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103021Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103079Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103167Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103234Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103289Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103342Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103393Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103463Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103522Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103596Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103647Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103697Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103748Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:16.103796Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.834095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:53:46.843170Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.843387Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.847063Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.848012Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.849389Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.850115Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.850581Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.850979Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.851245Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.851778Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.853101Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.854063Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.856002Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.856564Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.859870Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.860187Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.863821Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.863999Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864064Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864114Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864177Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864268Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864358Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864411Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864460Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864512Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864558Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864603Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864649Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864694Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864738Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864783Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864829Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864875Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T06:53:46.864934Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.354190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:06:53.367163Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.367929Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368421Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368556Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368640Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368721Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368791Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368905Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.368996Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369058Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369107Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369156Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369202Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369254Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369318Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369369Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369432Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369494Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369548Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369590Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369631Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369673Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369715Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369758Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369803Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369847Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369890Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369935Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.369978Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370020Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370063Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370105Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370146Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370188Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:06:53.370229Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.096606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:07:24.302573Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.329430Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.329824Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.329993Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330123Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330229Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330354Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330497Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330624Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330704Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330816Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.330925Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331025Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331108Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331208Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331317Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331573Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331692Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331843Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.331948Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332057Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332138Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332219Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332299Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332410Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332501Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332583Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332658Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332763Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332863Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.332965Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.333035Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.333106Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.333176Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:07:24.333238Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.790561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:23:19.802453Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.808094Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.808260Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.809661Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.809956Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.810397Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.811339Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.811696Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.812308Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.812515Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.813280Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.813607Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.814315Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.814553Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.815310Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.815588Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.816568Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.817310Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.817581Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.818091Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.818252Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.818373Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.818430Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.819286Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.821632Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.822320Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.823312Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824101Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824186Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824256Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824340Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824398Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824445Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824493Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:23:19.824538Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.352790","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:39:14.366371Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368261Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368473Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368560Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368624Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368696Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368769Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368852Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368929Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.368995Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369052Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369110Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369173Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369233Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369307Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369364Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369409Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369469Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369556Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369605Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369650Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369694Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369740Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369785Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369830Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369876Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.369957Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370044Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370123Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370184Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370238Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370285Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370344Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370409Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:39:14.370459Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.015195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:55:09.022932Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.027181Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.029166Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.029793Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.029896Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030180Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030264Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030344Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030417Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030491Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.030571Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.032153Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.032577Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.032966Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.034184Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.035187Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.036285Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.041889Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042074Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042134Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042187Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042234Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042281Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042325Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042371Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042416Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042461Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042506Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042551Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042595Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042639Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042684Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042728Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042774Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:09.042817Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.785030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:55:39.796440Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.796692Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.802422Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.802569Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.805252Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.805522Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.805597Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.806208Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.807238Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.807662Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809025Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809229Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809469Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809637Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809734Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809795Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809854Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809912Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.809961Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810009Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810054Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810107Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810172Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810217Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810260Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810303Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810347Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810389Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810429Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810472Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810539Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810582Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810625Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.810670Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T07:55:39.813278Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.267559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:07:53.272368Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.272577Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.272691Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.273275Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.273505Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.275714Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.275896Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.275962Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276031Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276087Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276133Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276177Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276222Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276266Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276314Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276371Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.276416Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.282350Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.282806Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.284103Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.284667Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.284842Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.284952Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.285747Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.286108Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.286790Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.286962Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.287738Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288340Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288411Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288468Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288521Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288587Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288651Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:07:53.288756Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.681408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:23:48.695725Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.696110Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.696721Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.696912Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.697811Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.697914Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.697987Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698064Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698149Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698238Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698305Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698369Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698441Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698514Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698614Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698698Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.698776Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.699858Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.700684Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.701024Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.701809Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.701895Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.701987Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702050Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702099Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702153Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702228Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702319Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702399Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702474Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702558Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.702643Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.710527Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.710821Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:23:48.710998Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.342130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:24:19.349187Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.349678Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.349788Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.349900Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.349979Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.351257Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.351504Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.352744Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.353085Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.353261Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.353711Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.354733Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.356665Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.356789Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.356876Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.356959Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357109Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357193Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357278Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357357Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357436Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357512Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357583Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357665Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357732Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357812Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.357962Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.358076Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.358179Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361046Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361358Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361440Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361502Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361556Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:24:19.361619Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.780593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:40:14.788161Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.788817Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.790787Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.792872Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.793794Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.793990Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794079Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794173Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794248Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794316Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794386Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794446Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794502Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794567Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794780Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794857Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.794945Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795045Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795125Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795197Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795296Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795356Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795403Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795470Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795555Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795622Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795700Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795788Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795863Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.795957Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.796032Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.796103Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.799000Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.799216Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:14.799312Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.401870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:40:45.408487Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.409020Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.409149Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.410068Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.410329Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.411812Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.411941Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412007Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412068Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412205Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412315Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412376Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412431Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412481Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412525Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412576Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.412622Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417016Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417211Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417307Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417386Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417458Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417529Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417605Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417676Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417746Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417833Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417910Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.417997Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.418069Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.418140Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.418210Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.418293Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.418427Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:40:45.419870Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.885756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:56:39.890722Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.894160Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.894453Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.895179Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.895354Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.895623Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.897013Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.899486Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.900066Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.900956Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.901921Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.902960Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.903724Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.903882Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.903997Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.904933Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.908647Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.908837Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.908925Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.908977Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909027Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909073Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909117Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909162Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909254Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909532Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909721Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909779Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909824Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909873Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909940Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.909983Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.910029Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.910079Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T08:56:39.910126Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.526419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:08:49.534425Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.537662Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.538384Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.538840Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.539161Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.539440Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.539605Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.540584Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.543686Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.544122Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.544403Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.544529Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.544666Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.547687Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.548510Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.548604Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.548661Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551380Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551456Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551511Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551630Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551713Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551784Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551840Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551895Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551947Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.551998Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552048Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552098Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552148Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552199Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552251Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552301Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552355Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:08:49.552403Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.270967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:09:20.283171Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.283601Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.283831Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.284024Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.284712Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.284919Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.285155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.285253Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.285829Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.298357Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.303612Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.305782Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.307661Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.308347Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.309104Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.309591Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.310028Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.318893Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.319412Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.319500Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.322302Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.324953Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.327969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.328924Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.329973Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330491Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330594Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330671Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330721Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330771Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330834Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330927Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.330999Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.331074Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:09:20.331137Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.881022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:25:13.890920Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.891242Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.891704Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.891946Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.892220Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.892653Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.893312Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.894482Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.894989Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.895274Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896126Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896225Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896387Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896470Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896540Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896614Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896712Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896775Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896831Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896901Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.896957Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.897017Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.897081Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.897172Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.897237Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.898606Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.899164Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.899325Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.899639Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.899871Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.900024Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.900170Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.905781Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.905964Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:25:13.906709Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.301349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:41:10.306871Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.307192Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.309145Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310119Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310632Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310696Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310743Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310788Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.310912Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.311004Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.311056Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.311903Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.312122Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.313150Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.314587Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.314954Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.318777Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.318969Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319049Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319113Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319162Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319209Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319266Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319314Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319401Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319470Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319532Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319582Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319628Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319671Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319715Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319787Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319833Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319886Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:10.319938Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:40.992205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:41:41.000143Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.000466Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001288Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001401Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001461Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001522Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001586Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001639Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001692Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001756Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001818Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001871Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001925Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.001985Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.002214Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.002868Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.003334Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.004061Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.005792Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.007346Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.008336Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.009223Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.009389Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.009491Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.011344Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012323Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012425Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012502Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012557Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012610Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012675Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.012761Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.015142Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.015227Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:41:41.015299Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.488020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:57:35.495621Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.498488Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499451Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499538Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499597Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499646Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499697Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499745Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499787Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.499830Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.504445Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505053Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505161Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505228Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505298Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505354Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505414Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505493Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505612Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505672Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505730Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505794Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505861Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.505938Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506004Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506067Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506123Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506169Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506219Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506277Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506353Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506436Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506486Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506549Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T09:57:35.506604Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.080464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:09:44.088877Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.090269Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.090711Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.091262Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.092145Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.092506Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.092870Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.094526Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.095336Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.095522Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096213Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096295Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096384Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096438Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096493Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096591Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096661Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096724Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.096781Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097636Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097704Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097750Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097795Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097840Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097886Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097936Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.097980Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098024Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098070Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098119Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098204Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098266Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098338Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098435Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:09:44.098482Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.858848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:10:14.869033Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.869474Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.869592Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.869702Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.869795Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.869909Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870069Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870159Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870259Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870418Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870651Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870727Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.870908Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.871017Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.871111Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.871161Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.871233Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.876776Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.876937Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877004Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877054Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877104Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877154Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877201Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877247Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877291Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877336Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877379Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877423Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877468Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877512Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877555Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877598Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877643Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:10:14.877686Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.214847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:26:10.223873Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.224915Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.225104Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.225554Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.225654Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.225915Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.226159Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.226313Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.226434Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.226580Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.226738Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227156Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227253Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227449Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227556Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227683Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227757Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227843Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227919Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.227986Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228098Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228168Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228233Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228303Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228368Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228443Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228520Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228580Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228640Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228766Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228821Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.228867Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.231842Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.231992Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:26:10.232055Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.619896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:42:05.636360Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.638255Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.640044Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.641353Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.642136Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.642330Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.642493Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.643105Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.643458Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646233Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646439Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646522Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646596Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646679Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646777Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646862Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.646945Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647026Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647121Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647207Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647307Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647408Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647514Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647641Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.647717Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648168Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648262Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648372Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648488Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648594Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648674Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648744Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648816Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648897Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:05.648996Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.212308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:42:36.232300Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.235236Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.235917Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.237023Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.238989Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.240261Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.240941Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.242851Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243055Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243193Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243305Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243392Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243473Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243566Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243648Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243754Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.243920Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244041Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244122Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244189Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244256Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244396Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244503Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244580Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244655Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244727Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244838Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.244960Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.245037Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.245085Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.245131Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.245194Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.251090Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.251351Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:42:36.251444Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.728719","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:58:31.738708Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.739990Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.743919Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744034Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744109Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744175Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744231Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744284Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744333Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744395Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744560Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744712Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744813Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744895Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.744961Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.745024Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.747757Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.747901Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.747965Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748037Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748115Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748168Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748229Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748282Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748327Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748377Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748433Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748484Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748540Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748589Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748635Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748689Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748744Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748797Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:58:31.748854Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.386521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:59:02.397322Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.397580Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.398605Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404157Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404440Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404503Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404550Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404605Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404651Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404696Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404740Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404784Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404828Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404885Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.404949Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.405003Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.405057Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.408574Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.408730Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.408821Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.408910Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.408986Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409061Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409128Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409193Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409294Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409386Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409472Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409555Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409628Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409683Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409767Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409837Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.409914Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T10:59:02.410038Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.564531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:11:00.578431Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.578806Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.578946Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.579906Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.580979Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.581509Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.581885Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.583007Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.583773Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.585109Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.585709Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.586712Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.588695Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.588884Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.588955Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589044Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589109Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589177Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589246Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589305Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589360Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589424Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589476Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589531Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589589Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589672Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589743Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589859Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589926Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.589985Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.590045Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.590096Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.590148Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.590202Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:11:00.590255Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.153613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:26:55.196497Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196591Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196673Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196755Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196805Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196875Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.196938Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197012Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197078Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197129Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197175Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197224Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197277Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197338Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197417Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197472Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197532Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197615Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197673Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197717Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197762Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197834Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197878Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197921Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.197966Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198011Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198057Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198110Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198156Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198223Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198296Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198345Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198392Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198439Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:26:55.198483Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.804266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:27:25.809928Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.810140Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.810728Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.810886Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.811005Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.813550Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814210Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814416Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814530Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814609Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814667Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814731Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814788Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814852Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814945Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.814996Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.815059Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.817769Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.817913Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.817985Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818058Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818117Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818168Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818223Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818279Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818346Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818422Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818508Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818583Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818658Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818714Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818768Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818823Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818894Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:27:25.818977Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.185504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:43:21.191762Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.191996Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.195560Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.196171Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.196439Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200107Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200497Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200601Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200703Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200764Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200813Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200883Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200942Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.200991Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.201036Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.201091Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.201160Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.204532Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.204694Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.204801Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.204866Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.204937Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205012Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205074Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205141Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205203Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205279Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205344Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205528Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205646Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.205700Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.207794Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.209114Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.209190Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:43:21.209235Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.950915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:59:15.963709Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.964689Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.964832Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.964909Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.964988Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965059Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965127Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965633Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965734Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965794Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.965855Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.968619Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.969288Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.969404Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.969550Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.969694Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.969785Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972553Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972682Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972764Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972848Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972900Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.972960Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973020Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973087Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973140Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973193Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973246Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973309Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973365Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973411Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973479Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973564Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973679Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T11:59:15.973749Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.659316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:11:37.665238Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.665487Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674417Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674614Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674714Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674792Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674910Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.674999Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675086Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675180Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675239Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675295Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675351Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675405Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675460Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675532Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675595Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.675665Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.676598Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.677560Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678541Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678610Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678660Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678704Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678754Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678831Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678880Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678933Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.678984Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679033Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679120Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679162Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679207Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:11:37.679248Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.350779","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:12:08.357710Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.358018Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.358706Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.358867Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.359712Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.361715Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.363176Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.363447Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.363655Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.366657Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.366814Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367155Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367258Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367320Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367382Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367440Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.367485Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374224Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374420Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374524Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374595Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374660Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374733Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374801Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374850Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374896Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.374942Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375005Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375085Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375160Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375206Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375252Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375297Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375344Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:12:08.375390Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.732678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:28:02.742059Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.742913Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.743146Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.744328Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.746611Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749382Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749593Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749717Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749778Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749833Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749899Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.749959Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.750032Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.750077Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.750125Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.750170Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.750215Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752392Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752476Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752526Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752581Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752648Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752704Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752751Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752798Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752845Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752893Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752937Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.752979Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753021Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753064Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753108Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753152Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753196Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:28:02.753237Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.146749","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:43:58.158350Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.158611Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.159286Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.160314Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.160732Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.161504Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.163316Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.163590Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.164576Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.165263Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.165389Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.165511Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.165562Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.166623Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.167580Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169373Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169491Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169566Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169628Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169680Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169727Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169772Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169816Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169860Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169904Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169952Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.169996Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170040Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170083Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170127Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170172Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170215Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170266Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170313Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:43:58.170358Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.816232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:44:28.831110Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831316Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831441Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831540Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831602Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831674Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831736Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831803Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831858Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831911Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.831965Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.832018Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.832062Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.833389Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834367Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834480Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834550Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834619Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834676Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834723Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834769Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834814Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834880Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834932Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.834977Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835022Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835067Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835112Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835158Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835340Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835393Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835437Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835481Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835538Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T12:44:28.835590Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.126184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:00:24.137047Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.140618Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.140829Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.140948Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141007Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141121Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141195Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141294Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141403Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141472Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141603Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.141785Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142176Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142279Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142366Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142440Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142520Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142626Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142704Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142769Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142832Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142893Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.142965Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143038Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143082Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143124Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143167Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143210Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143252Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143294Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143336Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.143379Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.150791Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.150982Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:24.151038Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.875276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:00:54.883263Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.898366Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.899104Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.899522Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.899926Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.900863Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.901148Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.901313Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.901464Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905025Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905223Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905287Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905348Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905403Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905473Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905555Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905625Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905729Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905791Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905853Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905898Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905945Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.905990Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906035Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906081Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906133Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906178Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906224Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906267Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906310Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906354Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906398Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906448Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906493Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:00:54.906538Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.709335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:13:05.724660Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.724988Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.727042Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.728040Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.728258Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.728692Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.729362Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.730206Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.731022Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.731313Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.732275Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.732995Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.733322Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.734263Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.735313Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.735973Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.736100Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.736227Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.737026Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.737318Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.738271Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.739093Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.739289Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740071Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740198Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740287Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740364Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740422Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740469Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740514Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740560Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.740605Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.743196Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.743256Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:13:05.743305Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.042392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:29:03.054171Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.054383Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.054515Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.054620Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058510Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058668Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058729Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058786Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058833Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058881Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.058935Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.059709Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.060289Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.060497Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.061286Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.061616Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.062560Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063262Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063341Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063400Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063453Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063497Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063548Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063605Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063753Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.063877Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.064604Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.065614Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.065730Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.066323Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.066494Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.067285Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.070218Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.070372Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:03.070459Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.697717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:29:33.706358Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.706640Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.706808Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.708196Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.708850Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.710150Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.710939Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.711473Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.711819Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.712120Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.712750Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.713106Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.713256Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.713930Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.714611Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.715481Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.716873Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.717347Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.717629Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.718336Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.718971Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.720201Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.720846Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.721560Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.721992Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.722457Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.722839Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.723829Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.724349Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.724625Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.725644Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.725960Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.732503Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.732955Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:29:33.733103Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.525802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:45:27.536776Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.539544Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.541050Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.555760Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.556597Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.557449Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.557549Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.558028Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.558268Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.558648Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.558853Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.559197Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.559509Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.561223Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.561637Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.562650Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.563474Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.563751Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.563967Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.564271Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.565671Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.565904Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.566004Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.566227Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.566533Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.566842Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567040Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567135Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567237Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567306Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567389Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567592Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567718Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567784Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T13:45:27.567861Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:22.998532","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:01:23.025169Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.027869Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.030456Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.030956Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.031152Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.033256Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.034184Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.036450Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.038053Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.038545Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.038747Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.038942Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.039169Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.039420Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.041053Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.044284Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.044931Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054011Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054335Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054487Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054573Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054638Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.054829Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.055114Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.055339Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.055518Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.055613Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.055906Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.056073Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.056140Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.056521Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.056603Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.056761Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.057016Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:23.057297Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.842898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:01:53.855638Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.855890Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856113Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856236Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856379Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856536Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856649Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856754Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.856936Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857061Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857165Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857258Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857360Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857515Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857626Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857746Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857873Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.857991Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858095Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858194Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858281Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858433Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858587Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.858655Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.859211Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.859396Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.859590Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.860017Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.860142Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.860909Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.861977Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.862428Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.863029Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.863137Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:01:53.863205Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.064227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:13:53.072074Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.073903Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.079909Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080067Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080137Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080193Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080254Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080336Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080406Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080470Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080523Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080571Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080613Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080667Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080720Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080785Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080857Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080925Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.080987Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.081031Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.081072Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.081117Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.081166Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.081891Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.082094Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.082903Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.083048Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.083107Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.083197Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.083955Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.084167Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.085137Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.086994Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.087054Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:13:53.087101Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.157009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:15:54.168937Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.169184Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.172619Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.172785Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.172869Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.172984Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173056Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173144Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173210Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173287Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173341Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173398Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173468Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173520Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173564Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173623Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173678Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173726Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173777Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173819Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173863Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173903Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173942Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.173982Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.174058Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.174138Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.176702Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.176790Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.176911Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.176996Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.177275Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.177355Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.179909Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.179987Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:15:54.180038Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.507768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:31:49.513488Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.516625Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.516842Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.516943Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517040Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517110Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517179Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517255Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517326Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517438Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517579Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517666Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517741Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517800Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517875Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517937Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.517996Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.520943Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.521268Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.521740Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.522996Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.523883Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.524357Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.524482Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.524600Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.525938Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527420Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527546Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527604Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527664Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527720Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527780Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527863Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527930Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:31:49.527984Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.196844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:47:45.204143Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.210939Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.211316Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.211452Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.211510Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214169Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214285Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214357Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214414Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214462Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214509Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214558Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214601Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214697Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214752Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214812Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214858Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214919Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.214973Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215017Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215057Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215098Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215141Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215182Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215248Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215295Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215344Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215400Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215470Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215538Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215612Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215679Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215766Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215830Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:47:45.215888Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.811113","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:48:15.822893Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823030Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823145Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823235Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823332Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823458Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823555Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823712Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.823829Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824146Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824219Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824386Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824483Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824573Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824617Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824695Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.824780Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830431Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830504Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830554Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830600Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830646Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830693Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830738Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830783Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830826Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830868Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830909Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830950Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.830992Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.831033Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.831075Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.831118Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.831161Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T14:48:15.831204Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.395764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:04:10.407833Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408114Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408194Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408259Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408328Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408382Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408444Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408542Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408634Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408710Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408798Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408905Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.408976Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409055Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409122Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409197Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409257Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409336Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409430Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409497Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409609Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409684Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409746Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409796Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409873Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.409944Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410099Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410211Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410277Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410324Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410418Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410503Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410581Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410648Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:04:10.410785Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.381018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:14:45.387671Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.398591Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.398783Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.398871Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.398933Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.398984Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399041Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399116Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399172Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399270Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399327Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399383Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399448Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399549Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399639Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399699Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399795Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399869Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.399941Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400056Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400150Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400219Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400278Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400327Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400412Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400477Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400542Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400592Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400652Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400714Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400792Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400847Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400909Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.400964Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:14:45.401032Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.062896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:15:16.067170Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.070340Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.070611Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071201Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071351Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071449Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071518Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071596Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071815Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071926Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.071985Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.072221Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.072402Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.072596Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.074050Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.074143Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.074272Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.078914Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.080075Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.080229Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.080331Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.080409Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.082105Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083092Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083543Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083621Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083688Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083740Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083792Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083844Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083895Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083946Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.083997Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.084090Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:15:16.084148Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.632816","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:31:11.644148Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.644306Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.644386Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.645600Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.645688Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.645767Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.645860Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.645918Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646000Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646077Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646155Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646267Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646337Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646400Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.646783Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.647558Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.647853Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.648030Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.648676Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.649895Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.650533Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.650738Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651504Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651590Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651641Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651685Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651729Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651772Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651832Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651875Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651918Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.651960Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.658966Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.659025Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:31:11.659070Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.937471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:47:06.944386Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.944903Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.945048Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.945168Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.945254Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.946352Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.946544Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.947715Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.948791Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.949368Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.949665Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.950694Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.951101Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.951888Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.952759Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.952992Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.953919Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961522Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961597Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961669Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961717Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961783Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961829Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961874Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961919Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.961966Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962010Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962054Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962095Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962136Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962177Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962218Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962260Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962303Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:06.962368Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.651205","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:47:37.664971Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665181Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665285Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665375Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665503Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665604Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665728Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665842Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.665935Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666025Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666110Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666207Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666288Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666380Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666462Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666545Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666617Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666693Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666766Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666840Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666900Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.666972Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667037Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667106Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667183Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667247Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667312Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667378Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667494Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667555Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667621Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667691Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667756Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667814Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T15:47:37.667861Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.455184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:03:19.460575Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.463511Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464002Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464221Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464316Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464402Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464474Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464551Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464629Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464717Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464799Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464876Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.464946Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.465017Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.465095Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.465178Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.465239Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.471721Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.472503Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.472784Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.473794Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.473922Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474025Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474081Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474136Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474189Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474242Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474294Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474345Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474394Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474445Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474542Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474614Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474669Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:03:19.474726Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.357975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:15:46.366792Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.368085Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.369872Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.370347Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.371903Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.374602Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.374737Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.374801Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.374868Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.374935Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375002Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375058Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375106Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375162Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375228Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375310Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375357Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375410Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375481Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375675Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375773Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375833Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375885Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.375949Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376007Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376089Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376158Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376209Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376265Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376312Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376358Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376414Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376481Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376567Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:15:46.376660Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.088976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:16:17.100322Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.100491Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.101262Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.101344Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.101466Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105324Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105528Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105607Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105675Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105771Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105828Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105876Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105921Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.105997Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106060Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106120Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106167Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106219Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106275Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106321Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106365Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106408Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106474Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106518Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106561Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106645Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106702Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106759Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106819Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106882Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106931Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.106983Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.107056Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.107151Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:16:17.107201Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.393287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:32:12.400699Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.400972Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.403752Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.404999Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.405113Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.405698Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.406505Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407616Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407703Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407757Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407816Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407870Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407913Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.407956Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.408020Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.408083Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.408135Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414027Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414440Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414518Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414579Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414634Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414807Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414864Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414913Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.414958Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415005Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415052Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415096Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415139Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415181Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415222Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415264Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415333Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:32:12.415376Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.710237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:48:06.719666Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.720640Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.720748Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.721032Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.722146Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.724563Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.724918Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.725265Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.725511Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.725880Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.726111Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.727331Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.727912Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.728167Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.728305Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.728563Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.728716Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.729007Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.729437Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.729649Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.729851Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.730097Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.730265Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731019Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731106Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731198Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731329Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731400Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731458Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731503Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731553Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.731609Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.742345Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.742418Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:06.742469Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.292000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:48:37.302131Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.303662Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.304638Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.305596Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.307716Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.308180Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.308754Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.308930Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.312813Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.315780Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.319345Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.319927Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.320070Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.320241Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.320387Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.320576Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.324479Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326416Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326558Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326683Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326741Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326796Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326848Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326900Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.326952Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327010Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327060Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327111Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327160Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327210Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327259Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327310Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327360Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327416Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T16:48:37.327465Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.726030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:04:32.737679Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.738464Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.738682Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.738850Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.738997Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.740574Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.741075Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.741464Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.741653Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.741802Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742010Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742144Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742289Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742571Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742778Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.742914Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743000Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743090Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743402Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743555Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743755Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743861Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.743982Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.744148Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.744273Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.744379Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.745530Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.745772Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.746730Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.747795Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.748030Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.748189Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.748271Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.748351Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:04:32.748409Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.001005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:16:44.012103Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.012968Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.014916Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016139Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016360Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016468Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016543Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016641Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016784Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.016987Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.017947Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.019919Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.020895Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021565Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021662Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021715Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021765Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021833Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021890Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021936Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.021982Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022027Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022113Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022182Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022233Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022281Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022327Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022392Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022438Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022481Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022527Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.022569Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.025132Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.025285Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:16:44.025355Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.644526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:17:14.654058Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.654762Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.654854Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.654944Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.655001Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.655048Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.655101Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.655671Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.655876Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.656014Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.656718Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657678Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657769Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657837Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657884Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657931Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.657985Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.665018Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.665959Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.666123Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.666232Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.666280Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.666999Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.669046Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.669691Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670422Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670490Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670538Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670585Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670630Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670717Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670778Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670845Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670910Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:17:14.670964Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.930705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:33:00.940816Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943029Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943224Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943299Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943381Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943437Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943498Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943578Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943648Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943720Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943788Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943893Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.943949Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944045Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944103Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944149Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944202Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944263Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944325Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944389Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944439Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944480Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944520Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944561Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944630Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944684Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944734Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944773Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944813Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944853Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944898Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944943Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.944999Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.945041Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-20T17:33:00.945081Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
