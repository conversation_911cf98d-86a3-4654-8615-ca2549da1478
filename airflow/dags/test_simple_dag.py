"""
Simple test DAG to verify Airflow is working correctly.
This DAG doesn't import any shared modules to isolate import issues.
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.decorators import task

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timed<PERSON>ta(minutes=5),
}

@task
def hello_world():
    """Simple hello world task"""
    print("Hello World from Airflow!")
    return "success"

@task
def goodbye_world():
    """Simple goodbye task"""
    print("Goodbye World from Airflow!")
    return "success"

# Create the DAG
with DAG(
    'test_simple_dag',
    default_args=default_args,
    description='A simple test DAG',
    # schedule_interval=None,  # Manual trigger only
    catchup=False,
    tags=['test'],
) as dag:
    
    hello = hello_world()
    goodbye = goodbye_world()
    
    hello >> goodbye
