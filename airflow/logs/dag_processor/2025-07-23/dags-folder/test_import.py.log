{"timestamp":"2025-07-23T00:10:55.083329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:10:55.087782Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.088887Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.090925Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.091238Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.100246Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.100834Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.100953Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101013Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101065Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101119Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101175Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101230Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101287Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.101342Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.102228Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.103131Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.103877Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.104835Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.104907Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.104954Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105000Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105044Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105089Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105132Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105185Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105243Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105287Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105328Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105374Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105464Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105508Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105553Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105596Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105641Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:10:55.105685Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.344456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:11:25.361425Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.361641Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363067Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363206Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363268Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363320Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363376Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363432Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363480Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363525Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363585Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363649Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363713Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363786Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363861Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363916Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.363970Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364039Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364094Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364139Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364181Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364223Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364293Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364400Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364451Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364493Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364535Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364576Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364616Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364657Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364711Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.364770Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.367351Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.367509Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:11:25.367576Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.775490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:27:20.785204Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785403Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785472Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785529Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785590Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785641Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785689Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785740Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785787Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785894Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.785980Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786037Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786101Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786187Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786260Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786331Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786450Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786537Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786614Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786686Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786735Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786784Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786834Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786882Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786930Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.786987Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787037Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787086Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787144Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787188Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787235Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787302Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787367Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787424Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:27:20.787470Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.848588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:15.856947Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.857367Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.858383Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.860260Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.861248Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.864496Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.864746Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.864822Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.864880Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.864929Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865414Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865523Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865608Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865693Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865769Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865855Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.865938Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.869785Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.869948Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870011Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870077Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870125Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870189Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870247Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870302Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870355Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870420Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870478Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870527Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870579Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870638Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870730Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870817Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870893Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:15.870944Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.209702","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:46.218156Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.218716Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.218811Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.218875Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.218984Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.219039Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.219085Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.219134Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.219297Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.221040Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.222966Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223158Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223238Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223315Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223387Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223446Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.223491Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.226862Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227076Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227182Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227283Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227352Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227434Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227548Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227648Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227738Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227796Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227857Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.227975Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228064Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228163Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228236Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228324Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228415Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:43:46.228523Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.689916","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:59:41.703060Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.703377Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.703613Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.704126Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.705236Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.707669Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708470Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708591Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708702Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708801Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708863Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708909Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708954Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.708998Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.709044Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.709100Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.709154Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.711840Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.711988Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712092Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712185Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712255Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712324Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712429Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712486Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712549Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712603Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712700Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712762Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712822Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.712941Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.713031Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.713123Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.713193Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T00:59:41.713271Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.448101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:00:12.452704Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.452919Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.453047Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.453134Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.453218Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.453295Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.453820Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454006Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454098Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454160Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454215Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454278Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454333Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454389Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454448Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454521Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.454587Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.459233Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.459935Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460033Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460094Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460184Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460231Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460276Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460322Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460372Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460416Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460463Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460506Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460550Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460596Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460640Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.460687Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.461286Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:00:12.462210Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.977704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:16:06.983519Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.984512Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.994765Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.994946Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995031Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995107Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995197Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995288Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995363Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995425Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995475Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995533Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995591Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995688Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995815Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995890Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.995958Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996027Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996097Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996169Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996239Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996294Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996358Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996413Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996459Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996510Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996564Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996687Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996776Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996844Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996903Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.996972Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.997053Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.997130Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:16:06.997188Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.040956","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:02.053956Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.055983Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.056852Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.057534Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.057731Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.057871Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.057936Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.057986Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058082Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058131Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058196Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058264Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058337Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058409Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058475Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058530Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058610Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058679Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058740Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058818Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058890Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.058957Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059006Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059050Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059096Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059159Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059241Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059338Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059399Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059457Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059514Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.059563Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.062045Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.062208Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:02.062314Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.795924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:32.805983Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.806795Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.807744Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.808800Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.809233Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.809790Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.810795Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.811750Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.811846Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.811936Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.811989Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812038Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812083Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812139Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812194Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812249Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812299Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812343Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812390Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812436Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812478Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812524Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812571Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812613Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812660Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812708Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812764Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812809Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812850Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.812903Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.813740Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.813802Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.813911Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.813958Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:32:32.814007Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.651085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:48:27.658086Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.658410Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.658488Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.659014Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.659203Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663121Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663532Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663620Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663696Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663754Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663816Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663866Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663914Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.663973Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.664041Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.664090Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.664146Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.670354Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.674564Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.675451Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.676550Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.677242Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.678253Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.679233Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.680254Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681294Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681363Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681412Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681457Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681501Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681545Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681587Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681630Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681672Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T01:48:27.681713Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.362052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:23.381945Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382229Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382377Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382477Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382586Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382738Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.382880Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383005Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383104Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383205Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383304Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383382Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383463Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383643Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383803Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.383923Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.384036Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.384194Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.384329Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.384578Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.384734Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.385039Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.386390Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.386708Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387021Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387227Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387381Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387558Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387673Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387823Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.387951Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.388066Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.388180Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.388305Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:23.388458Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.374313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:54.380341Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.381672Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.381788Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.382600Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.383650Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.385985Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.386978Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387107Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387239Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387347Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387482Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387643Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387772Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387859Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.387984Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388130Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388248Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388340Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388417Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388503Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388595Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388677Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388741Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388785Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388848Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.388941Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389016Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389078Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389129Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389203Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389270Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.389318Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.394829Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.395036Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:04:54.395107Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.736648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:20:48.744349Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.745460Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.745827Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.749815Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.750701Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.752820Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.754155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.754441Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.754751Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.754901Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.755113Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.755667Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.755877Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.756144Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.756373Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.756746Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.757047Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.757602Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758010Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758325Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758533Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758675Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758742Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758805Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.758915Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759001Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759100Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759179Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759302Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759436Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759516Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.759662Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.763065Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.763217Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:20:48.763297Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.215487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:36:44.220991Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.221649Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224382Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224592Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224659Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224732Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224792Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224847Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224903Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224949Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.224999Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.225076Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.225127Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.225197Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227416Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227560Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227628Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227714Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227774Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227827Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227870Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227913Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.227956Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.228001Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.228047Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.228087Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.228128Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.228172Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.229358Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230402Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230472Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230524Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230571Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230618Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:36:44.230661Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.103105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:37:15.112571Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.115696Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.115880Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.115953Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116338Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116527Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116598Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116686Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116751Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116816Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.116888Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.117523Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.119490Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.119650Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.119713Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.119778Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.119833Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.123057Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125483Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125609Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125667Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125723Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125768Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125851Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125898Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125942Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.125987Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126035Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126086Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126138Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126187Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126250Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126300Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126347Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:37:15.126404Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.741540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:53:09.748438Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.748951Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.749482Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.749591Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.750417Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752379Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752517Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752575Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752622Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752669Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752716Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752760Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752804Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752848Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.752892Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.753864Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.754012Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759504Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759682Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759753Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759806Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759857Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759916Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.759968Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760022Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760085Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760130Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760181Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760232Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760282Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.760380Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.761419Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.762370Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.763349Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T02:53:09.763451Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.875845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:07:52.882287Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.885146Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.885505Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.885724Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.888886Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889042Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889108Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889169Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889214Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889289Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889389Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889487Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889543Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889588Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889642Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889694Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.889739Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.891906Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892022Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892088Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892143Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892201Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892260Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892314Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892392Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892467Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892533Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892598Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892653Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892702Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892757Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892803Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892849Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892895Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:07:52.892939Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.810373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:08:23.816816Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.819282Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.819767Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.820276Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.820794Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.821252Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.823243Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.825758Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.825920Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828247Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828386Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828486Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828583Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828671Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828755Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828838Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828911Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.828995Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829107Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829183Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829287Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829368Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829635Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829814Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829885Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.829955Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830010Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830057Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830160Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830233Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830277Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830323Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830410Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830572Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:08:23.830844Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.325734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:24:19.336697Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337436Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337534Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337602Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337675Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337748Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337807Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337892Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.337948Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.338640Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339038Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339112Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339180Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339258Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339335Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339397Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339509Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339608Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339689Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339760Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339819Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339870Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339925Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.339980Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340028Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340111Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340191Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340291Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340355Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340411Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340456Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340499Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340549Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340634Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:24:19.340693Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.717571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:14.725964Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.726731Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.727094Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.727239Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.727474Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.730534Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.730790Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.731181Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.731560Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.733497Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.734451Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.735481Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.736480Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.737423Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.737490Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.737536Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.737581Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.743915Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744104Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744185Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744250Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744337Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744396Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744534Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744675Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744746Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744803Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744858Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744912Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.744970Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.745020Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.745063Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.745111Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.745158Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:14.745211Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.507815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:45.524813Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.525709Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.526441Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.527025Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.528304Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.529329Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.530340Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.530650Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.531154Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.531358Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.531673Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.532173Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.532430Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.532797Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.534096Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.535408Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.535979Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.536757Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.537129Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.537285Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.537372Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.537526Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.538389Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.538531Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.538689Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.538798Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.538979Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.539206Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.539365Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.539664Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.539913Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.540195Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.540325Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.540417Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:40:45.540517Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.906894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:56:39.921116Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921351Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921462Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921552Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921636Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921728Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.921947Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922224Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922341Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922481Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922560Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922636Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922719Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922818Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922891Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.922966Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923075Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923172Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923261Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923335Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923413Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923492Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923604Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923689Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923754Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923808Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923858Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.923941Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924022Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924118Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924213Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924307Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924364Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924424Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T03:56:39.924515Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.734308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:12:35.740968Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.741682Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.741985Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742047Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742114Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742161Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742205Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742259Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742317Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.742370Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.743533Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.743783Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.744752Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.745129Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.746734Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.748686Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.748838Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.748906Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.748971Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749020Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749064Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749112Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749200Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749245Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749293Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749338Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749382Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749433Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749480Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749526Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749572Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749648Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749702Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:12:35.749751Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.559080","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:13:06.564265Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.565006Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.565303Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.565881Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.567882Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.568587Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.571886Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.572169Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.572285Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.575301Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.575498Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.575663Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.575841Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.577239Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.577829Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.578035Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.578799Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581437Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581554Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581620Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581676Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581729Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581783Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581835Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581889Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.581939Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582049Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582138Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582213Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582274Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582328Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582391Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582451Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582518Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:13:06.582573Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.058924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:29:02.064539Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.064775Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.064938Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.066473Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.067490Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068449Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068564Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068639Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068710Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068777Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068848Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068903Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.068961Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069024Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069072Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069134Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069196Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069264Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069327Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069407Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069472Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.069528Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.070074Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.070148Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.070220Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.070289Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.070347Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.071039Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.071114Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.071175Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.071222Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.071876Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.076895Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.077316Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:29:02.077641Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.543757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:44:57.553788Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554071Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554166Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554230Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554310Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554419Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554523Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554631Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554752Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554846Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.554963Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555043Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555126Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555272Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555377Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555460Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555531Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555625Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555733Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555831Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.555938Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556008Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556098Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556156Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556238Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556324Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556427Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556518Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556595Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556677Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556727Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556798Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556889Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.556986Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:44:57.557077Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.476144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:45:28.490705Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.490902Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.490970Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491029Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491088Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491136Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491187Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491266Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.491342Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.492408Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.492710Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.493650Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494374Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494492Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494578Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494656Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494716Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494767Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494823Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494882Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494925Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.494968Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495014Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495056Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495105Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495155Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495198Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.495826Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.496591Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.497386Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.497698Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.498356Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.498445Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.498501Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T04:45:28.498547Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.882760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:05.900579Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.900910Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.901164Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.901374Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.901457Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.903171Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.903585Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.904880Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.904990Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.905638Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.905834Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.905953Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.906214Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.906317Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.906514Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.908180Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.909139Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.909847Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.909920Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.909967Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910012Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910054Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910099Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910141Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910184Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910226Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910268Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910309Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910350Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910391Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910461Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.910503Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.921068Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.921370Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:05.921429Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.042276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:37.047515Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.050319Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.050589Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.050817Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.051293Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.052377Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.052521Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053068Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053153Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053204Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053257Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053326Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053424Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053481Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053750Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.053806Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060116Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060310Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060392Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060467Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060582Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060659Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060709Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060790Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060895Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.060965Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061041Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061135Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061207Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061285Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061340Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061397Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061465Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061519Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:04:37.061570Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.959117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:20:30.964298Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.964773Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.965018Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.965134Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.965405Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.965665Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.965743Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.966796Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.968747Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.970155Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.970916Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.972622Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.972785Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.972952Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.973161Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.973296Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.973384Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.976649Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.976796Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.976867Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.976986Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977083Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977138Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977260Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977334Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977399Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977450Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977497Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.977543Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983582Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983724Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983782Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983835Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983890Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:20:30.983943Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.116675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:21:02.128275Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.129520Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.132068Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133347Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133494Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133564Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133625Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133694Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133755Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133803Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133865Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.133947Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.134000Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.134045Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.134517Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.134827Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.136953Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.140391Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.140589Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.140729Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.140858Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.140951Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141018Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141117Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141219Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141309Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141406Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141517Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141630Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141714Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141803Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141870Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.141939Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.142018Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:21:02.142086Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.652919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:36:56.658959Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659170Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659288Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659366Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659447Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659540Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659607Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659673Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659730Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659789Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659875Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.659957Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660046Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660117Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660180Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660240Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660298Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660374Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660433Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660478Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660523Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660571Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660621Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660680Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660733Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660796Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660847Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660901Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660948Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.660992Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.661040Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.661087Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.663274Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.663440Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:36:56.663591Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.249491","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:52:52.257540Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.257913Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.258474Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.260788Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.270521Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.270794Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.270969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271181Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271293Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271378Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271477Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271625Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271745Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.271920Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272106Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272241Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272361Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272453Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272548Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272636Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272723Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272811Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.272916Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273030Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273178Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273299Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273418Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273529Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273694Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273789Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273870Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.273953Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.274032Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.274176Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:52:52.274325Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.700426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:53:22.710617Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.710824Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.710904Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.710975Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.711037Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.711850Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.712384Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714192Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714305Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714362Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714408Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714461Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714524Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714593Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714662Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714721Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714776Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714849Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714906Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714952Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.714999Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715043Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715119Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715205Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715249Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715295Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715337Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715379Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715422Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715465Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715509Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715558Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715603Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715646Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T05:53:22.715687Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.157924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:09:18.173737Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.173937Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174008Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174104Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174214Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174304Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174384Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174451Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174528Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174605Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174675Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174734Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174805Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174899Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.174972Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175058Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175105Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175168Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175244Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175316Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175369Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175417Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175472Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175525Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175579Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175632Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175675Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175867Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.175952Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176008Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176066Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176146Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176228Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176284Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:09:18.176330Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.583520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:13.589250Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.589493Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.590467Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.592627Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.594455Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.594760Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596395Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596500Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596577Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596665Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596716Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596760Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596803Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596856Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596906Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.596997Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.597050Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599534Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599666Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599739Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599823Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599915Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.599974Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600023Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600072Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600117Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600167Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600222Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600279Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600344Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600405Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600453Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600499Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600570Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:13.600632Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.615949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:44.625547Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.626234Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.626441Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.629333Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.629793Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.629897Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.629964Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630020Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630069Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630136Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630191Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630265Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630332Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630385Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630464Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630526Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.630578Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633030Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633149Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633233Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633336Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633418Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633477Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633542Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633600Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633654Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633710Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633780Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633845Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633908Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.633954Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.634010Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.634081Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.634186Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:25:44.634244Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.505473","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:41:38.514712Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.516034Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.516966Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.518854Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.519291Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.521290Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.522024Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.522151Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.522894Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.524051Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.524990Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.525917Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.526105Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.527133Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.527453Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.527974Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.528281Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.537267Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.537476Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.537674Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.537828Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.537971Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.539068Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.539869Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.540838Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.540944Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541042Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541097Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541152Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541207Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541267Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541330Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541397Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541462Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:41:38.541521Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.070831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:49:33.081069Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.082043Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.083303Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.084321Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086148Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086249Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086316Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086370Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086425Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086492Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086574Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086634Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086703Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086755Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.086980Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.087189Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.087370Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.087554Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.087694Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.087862Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088089Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088218Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088309Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088401Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088593Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088694Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088858Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.088942Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.089034Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.089107Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.089210Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.089290Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.097320Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.097454Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:49:33.097508Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.376801","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:50:03.382734Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.383459Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.383556Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.383619Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.384292Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.385254Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.386178Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.386384Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.386561Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.386884Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.386977Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387060Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387141Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387224Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387271Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387316Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387364Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387420Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.387472Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.388446Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.388844Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.389174Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390150Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390226Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390273Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390320Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390366Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390409Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390453Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390495Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390537Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.390582Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.395651Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.396179Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T06:50:03.396328Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.337741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:05:41.343805Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.345965Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.347022Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.347192Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.347629Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.347763Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.347850Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.348035Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.348115Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.349963Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.350902Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.351013Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.351107Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.351172Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.351258Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.351337Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.360185Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.361136Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.362050Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.362409Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.363167Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.363396Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.363926Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.364225Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.364893Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.364979Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365071Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365148Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365222Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365310Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365390Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365468Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365555Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365635Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:05:41.365707Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.006486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:21:36.011310Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.014741Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.014938Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.016840Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.017807Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.018730Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.023844Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024086Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024190Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024276Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024370Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024433Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024488Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024552Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024620Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024680Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024752Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024836Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.024920Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025063Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025132Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025180Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025265Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025343Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025549Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025780Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.025921Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026066Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026126Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026189Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026274Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026324Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026423Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026528Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:21:36.026644Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.925483","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:22:06.939084Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.939584Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.940008Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.942598Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.942810Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.942934Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943046Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943155Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943244Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943349Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943650Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943770Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943881Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.943987Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944079Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944157Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944243Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944381Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944494Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944612Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944700Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944819Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944893Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.944951Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945045Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945116Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945207Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945281Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945337Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945413Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945499Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.945575Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.948607Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.948749Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:22:06.948869Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.406173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:37:49.452918Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453110Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453176Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453234Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453305Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453366Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453438Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453857Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.453979Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.454092Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.454171Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.454271Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.457544Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.457740Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.460180Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.460277Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.460337Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.463535Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.463685Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.463858Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.463915Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.463967Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.464018Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466460Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466612Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466666Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466715Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466904Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.466965Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467041Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467105Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467157Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467204Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467250Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:37:49.467310Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.298645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:25.303747Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.304630Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.308717Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.308866Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315194Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315399Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315483Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315571Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315641Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315702Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315764Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.315912Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316001Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316081Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316152Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316221Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316278Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316377Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316485Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316592Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316695Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316762Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316833Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316887Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316938Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.316990Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317042Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317094Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317146Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317206Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317272Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317336Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317404Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317476Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:25.317535Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.832551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:55.844068Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.844971Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845177Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845260Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845321Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845413Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845484Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845570Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845670Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845733Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845791Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845875Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845934Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.845999Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846139Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846196Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846264Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846344Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846431Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846485Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846529Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846584Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846672Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846730Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846791Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846842Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846924Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.846972Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847035Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847090Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847133Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847177Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847221Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T07:50:55.847263Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.386907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:06:50.397525Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410229Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410447Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410543Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410627Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410691Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410755Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410824Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.410931Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411031Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411105Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411175Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411239Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411316Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411395Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411465Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411526Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411589Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411652Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411713Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411761Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411813Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411872Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411918Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.411963Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412015Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412067Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412110Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412178Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412235Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412291Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412347Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412412Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412472Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:06:50.412516Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.211209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:07:21.221590Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.222262Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.222627Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.222894Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223059Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223344Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223523Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223678Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223792Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.223962Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.224384Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.224728Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.225247Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.226382Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.227510Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.230053Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.230399Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.230558Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.230774Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.230886Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231005Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231108Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231354Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231458Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231551Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231657Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.231786Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.232096Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.232486Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.232612Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.232716Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.232870Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.233048Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.233145Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:07:21.233407Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.740070","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:23:10.751812Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.753548Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.755570Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756024Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756113Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756177Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756247Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756305Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756359Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756435Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756518Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756584Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756637Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756728Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756784Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756840Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756928Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.756986Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757043Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757093Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757140Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757223Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757323Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757392Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757452Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757520Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757600Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757694Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757751Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757807Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757863Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.757913Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.760633Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.760806Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:23:10.760887Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.708086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:06.713480Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.715974Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.716708Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.717014Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.717178Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.717659Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.717983Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.718758Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.719122Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.719850Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.720181Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.720770Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.721396Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.722132Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.722708Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.722959Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.723217Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730383Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730549Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730613Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730663Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730711Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730756Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730808Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730851Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730905Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.730954Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731002Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731049Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731101Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731144Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731185Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731225Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731268Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:06.731310Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.517461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:37.522064Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.522258Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.522379Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.525896Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526006Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526087Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526142Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526208Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526268Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526319Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.526364Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.529143Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.529844Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.530178Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.531086Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.532087Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.532951Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.543000Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.543196Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.543273Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.543334Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.544941Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.546720Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.547211Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.547504Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.547959Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.548339Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.548787Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.549699Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.550208Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.550450Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.550938Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.551213Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.551360Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:39:37.551453Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.757528","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:51:41.766581Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.769866Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770095Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770185Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770245Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770295Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770377Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770456Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770527Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770587Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770647Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770711Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770769Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770835Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770893Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770943Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.770996Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771109Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771176Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771236Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771292Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771357Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771403Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771449Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771496Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771591Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771707Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771765Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771816Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771866Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.771939Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.772046Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.776928Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.777302Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T08:51:41.777454Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.581998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:07:36.591492Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.593088Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.593219Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.593290Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.593351Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.595159Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.596124Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.596823Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597079Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597159Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597227Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597300Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597375Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597439Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597496Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597570Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597634Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597731Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597788Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597850Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.597983Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598109Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598169Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598218Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598275Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598347Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598405Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598449Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598495Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598594Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598655Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.598704Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.601592Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.601780Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:07:36.601876Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.505948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:08:07.512392Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.512614Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.513267Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.513411Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.513660Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.514765Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.515611Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.515735Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.515818Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.515894Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.515964Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516036Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516108Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516182Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516312Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516391Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.516476Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.520706Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.520882Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.523060Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.524969Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.525666Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.526647Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.526743Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.526900Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.526956Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527017Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527085Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527146Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527257Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527311Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527371Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527429Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527488Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:08:07.527551Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.907101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:01.915933Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.916097Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.919573Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.919785Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.919865Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920102Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920330Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920404Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920538Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920618Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920702Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920803Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920902Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.920997Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921089Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921167Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921258Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921375Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921458Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921548Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921643Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921731Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921835Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921913Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.921991Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922060Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922145Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922225Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922301Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922363Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922439Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.922537Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.925674Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.925808Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:01.925892Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.795374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:32.803894Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.804975Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.805110Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.805360Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.805686Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.805839Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.806050Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.806311Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.806551Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.806730Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.807337Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.807536Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.807682Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.807847Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.807982Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808130Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808277Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808437Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808573Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808722Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.808863Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809144Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809331Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809485Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809577Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809663Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809720Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809768Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809835Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.809924Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.810018Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.810126Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.812640Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.812821Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:24:32.812923Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.490121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:40:27.500701Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502102Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502403Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502520Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502592Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502658Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502720Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502796Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502864Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502932Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.502980Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503027Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503085Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503152Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503214Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503265Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503310Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503359Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503409Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503501Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503563Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503635Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503728Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503829Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.503953Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504066Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504130Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504198Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504252Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504314Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504430Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504514Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504586Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504641Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:40:27.504726Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.308032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:52:39.322156Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.322441Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.322925Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323020Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323186Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323338Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323403Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323455Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323528Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323595Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323643Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323691Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323738Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323786Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323835Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323882Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323927Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.323973Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324056Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324107Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324158Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324217Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324273Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324330Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324391Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324473Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324570Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324627Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324677Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324747Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324802Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324872Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.324921Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.325012Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:52:39.325080Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.459991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:53:10.467240Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.467445Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.467617Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471125Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471435Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471522Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471593Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471651Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471701Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471767Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471848Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.471940Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472043Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472123Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472181Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472253Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472307Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472384Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472440Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472484Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472528Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472575Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472622Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472694Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472755Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472812Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472875Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472930Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.472983Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.473031Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.473077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.473130Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.475786Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.475944Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T09:53:10.476058Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.506763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:09:04.513192Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.516151Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.516769Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.520789Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.521141Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.521661Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.522724Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.523025Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.523518Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.523675Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.524177Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.524794Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.525023Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.526464Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.527208Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.527993Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528136Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528432Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528588Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528685Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528754Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528832Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.528931Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.529073Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.529244Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.529381Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.529465Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.530341Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.531039Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.531266Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.531984Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.532070Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.532130Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.532184Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:09:04.532235Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.267393","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:24:59.276262Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.283074Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.283874Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284233Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284303Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284356Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284409Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284470Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284526Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284573Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284620Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284671Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284717Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284769Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284816Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.284862Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.289840Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.290977Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291070Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291140Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291215Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291275Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291334Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291390Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291459Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291519Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291585Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291644Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291690Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291777Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291846Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291898Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.291947Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.292031Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:24:59.292096Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.210063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:25:30.215637Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.217645Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.219564Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.220494Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.224779Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.227405Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.227754Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.228518Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.229477Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230467Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230550Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230611Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230673Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230729Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230792Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230849Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230900Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.230961Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231022Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231070Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231112Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231186Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231283Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231442Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231557Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.231638Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.232508Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.234507Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.236200Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.236494Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.237494Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.238129Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.238485Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.239145Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:25:30.239519Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.340242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:41:24.354885Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.356505Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.358501Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.358700Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.358801Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.358995Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359131Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359234Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359317Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359416Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359559Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359656Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359745Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359875Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.359977Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.360063Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.360196Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.360302Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.360402Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.361571Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.362568Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363237Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363381Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363586Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363732Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363858Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.363964Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.364062Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.364169Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.364263Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.364373Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.364557Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.369398Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.369656Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:41:24.369803Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.371869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:28.382389Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.387868Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389058Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389220Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389325Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389402Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389518Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389614Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389712Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389793Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389892Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.389959Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390038Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390106Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390166Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390237Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390305Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390373Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390443Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390503Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390562Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390621Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390684Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390729Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390773Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390840Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390900Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.390949Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391005Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391067Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391123Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391169Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391217Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391276Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:28.391345Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.189893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:59.196927Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.198201Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.200216Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.200663Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.200883Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.200969Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.201071Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.202238Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.204161Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.206805Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.206949Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207030Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207105Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207197Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207272Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207347Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207420Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207493Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207567Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207630Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207698Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207752Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207803Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207866Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207920Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.207975Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208026Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208077Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208127Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208186Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208234Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208304Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208419Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208538Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T10:53:59.208732Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.754945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:09:53.761323Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.763222Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768252Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768427Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768511Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768571Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768622Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768687Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768752Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768816Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768861Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768915Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.768983Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769049Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769109Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769159Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769215Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769281Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769337Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769408Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769491Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769562Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769634Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769687Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769733Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769780Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769822Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769911Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.769982Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770055Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770127Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770197Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770252Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770300Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:09:53.770346Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.161451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:20:50.170984Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.171768Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.171908Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.171989Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.172070Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.172144Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.172216Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.172332Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.186767Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.186964Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187028Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187086Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187148Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187204Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187260Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187321Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187379Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187429Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187475Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187520Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187562Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187631Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187674Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187716Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187759Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187826Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187876Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187925Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.187967Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.188020Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.188067Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.188853Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.190868Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.190979Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:20:50.191053Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.305072","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:21:21.319559Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.319763Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.319845Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.319912Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.319995Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320076Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320156Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320218Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320315Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320400Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320482Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320596Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320673Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320746Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320844Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320916Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.320978Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321035Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321086Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321135Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321180Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321226Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321283Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321327Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321371Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321419Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321463Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321508Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321553Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321646Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321714Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321781Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321834Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321900Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:21:21.321950Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.738447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:53:22.777601Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.783906Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.786544Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.789483Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.789986Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.790294Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.790567Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.790753Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.791090Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.791350Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.791508Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.791787Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.792033Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.792282Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.792566Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.792938Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.793320Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.794006Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.794426Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.794662Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.794904Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.795105Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.795466Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.795667Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.795858Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.796093Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.796359Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.796572Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.796811Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.797530Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.798137Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.798706Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.799182Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.799627Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T11:53:22.800407Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.788537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:16.797687Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.797993Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.801637Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.801984Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.802765Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.804088Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.804696Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.805565Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.805761Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.805867Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.805969Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.806065Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.806160Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.806266Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.806384Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.807900Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.808648Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.817141Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.817441Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.817926Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.818267Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.818631Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.818944Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.819176Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.819449Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.819700Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.819903Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.820148Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.820405Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.820636Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.820892Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.821117Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.821579Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.821897Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:16.822349Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.954986","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:47.967538Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.968151Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.968339Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.969514Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.971653Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.971835Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.971970Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972065Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972156Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972208Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972319Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972389Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972434Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972530Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972644Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972735Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972823Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.972916Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973003Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973094Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973174Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973242Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973305Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973359Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973437Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973526Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973575Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973621Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973680Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973731Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973786Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.973846Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.977103Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.977255Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:09:47.977334Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.133776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:25:17.139211Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.139423Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.140253Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.140894Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.141744Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142273Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142373Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142463Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142545Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142611Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142671Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142726Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142791Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.142870Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.144343Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.145159Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.145452Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151487Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151636Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151723Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151775Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151820Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151866Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151910Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151954Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.151996Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152037Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152080Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152123Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152166Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152209Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152267Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152314Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152358Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:25:17.152405Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.648276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:12.656740Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.656862Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.656941Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.656998Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657049Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657233Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657299Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657457Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657586Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.657642Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659180Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659290Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659366Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659410Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659454Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659506Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.659553Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.664202Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.666222Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667278Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667402Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667488Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667595Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667749Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667902Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.667969Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668047Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668109Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668174Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668336Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668405Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668466Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668514Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668563Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:12.668607Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.651985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:43.660493Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.660906Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.661194Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.661852Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.662161Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.662473Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664209Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664298Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664362Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664413Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664458Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664511Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664582Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664637Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664690Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664745Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664799Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664855Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664902Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664947Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.664988Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665030Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665071Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665120Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665162Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665237Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665294Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665338Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665379Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665436Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665483Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665524Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665566Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665612Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:41:43.665653Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.908982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:54:38.923593Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.924268Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.925261Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.926499Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.926888Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.927263Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929317Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929480Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929554Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929638Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929715Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929783Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929847Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929914Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.929980Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930075Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930145Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930219Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930294Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930349Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930393Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930461Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930522Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930584Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930648Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930716Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930786Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930837Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930889Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.930995Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.931045Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.931097Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.931156Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.931211Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:54:38.931254Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.849014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:55:09.860742Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.861105Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.861316Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.863326Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865314Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865498Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865582Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865649Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865707Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865781Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865835Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865886Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.865975Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866062Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866129Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866177Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866231Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866319Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866381Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866445Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866502Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866549Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866592Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866676Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866736Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866783Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866827Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866870Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866917Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.866973Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.867029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.867091Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.871543Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.871709Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T12:55:09.871814Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.060217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:11:04.065244Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.065742Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.066211Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.066312Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.066500Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.067330Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.069291Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071288Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071393Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071450Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071502Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071552Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071602Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071667Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071739Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.071811Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.073304Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074349Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074432Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074492Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074537Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074581Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074625Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074667Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074712Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074754Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074797Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074838Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074878Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074921Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.074962Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.075002Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.075042Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.075083Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:11:04.075125Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.680563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:14:53.688437Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.688712Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.688828Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.688896Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.688952Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.689021Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.689088Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.689210Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.689336Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690264Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690367Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690428Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690479Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690521Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690564Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690619Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.690668Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.695499Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.696466Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.697401Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.698306Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.700333Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701344Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701432Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701489Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701540Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701593Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701641Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701693Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701745Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701791Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701837Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701880Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701928Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:14:53.701973Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.705500","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:21.725365Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.725585Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.725673Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.725746Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.725954Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726035Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726090Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726139Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726198Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726261Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726319Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726367Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726428Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726495Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726558Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726607Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726678Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726751Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726822Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726914Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.726972Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727026Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727080Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727128Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727174Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727232Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727289Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727338Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727385Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727432Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727520Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727592Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727649Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727739Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:21.727813Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.613372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:52.618847Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.619054Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.622626Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.623971Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.625917Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626108Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626236Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626319Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626391Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626451Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626503Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626554Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626601Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626662Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626725Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626775Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626824Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626891Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626949Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.626995Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627042Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627097Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627144Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627211Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627295Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627375Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627440Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627540Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627606Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627653Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627713Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.627771Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.630068Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.630172Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:30:52.630237Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.588419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:23.596362Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.600037Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.600195Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.600318Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.600788Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.600998Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601075Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601133Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601200Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601261Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601447Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601498Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.601597Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.604267Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.606195Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.606274Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.606329Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609017Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609148Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609243Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609331Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609418Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609465Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609511Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609559Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609604Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609652Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609698Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609758Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609824Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609875Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609926Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.609979Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.610040Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:23.610102Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.539710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:54.546632Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.548396Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.549514Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.551075Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553109Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553299Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553357Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553406Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553454Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553518Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553582Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553631Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553677Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.553731Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556189Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556386Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556482Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556556Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556614Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556664Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556715Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556762Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556808Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556853Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556914Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.556983Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557049Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557101Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557154Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557217Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557283Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557346Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557395Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557442Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:31:54.557488Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.479065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:25.488054Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.488469Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.489304Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.492839Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.493007Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.493769Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497362Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497559Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497649Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497719Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497766Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.497841Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.498816Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.498886Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.498935Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.499280Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.499360Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.507141Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.507377Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.507464Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.507533Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.507607Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.508787Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.509759Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.510728Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.510811Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.510861Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.510906Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.510951Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511012Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511069Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511112Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511155Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511199Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:25.511240Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.962578","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:57.980321Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.980970Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.981622Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.982292Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.984964Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.987925Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988072Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988146Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988198Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988252Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988312Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988376Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988422Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988467Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988515Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988566Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.988614Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991102Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991243Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991335Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991397Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991446Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991492Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991537Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991581Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991625Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991678Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991724Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991768Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991826Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991875Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991918Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.991987Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.992032Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:32:57.992075Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:45.991960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:50:46.006989Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.007792Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.008996Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009151Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009373Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009478Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009570Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009651Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009709Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009770Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009832Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009894Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.009974Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010047Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010115Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010176Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010235Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010296Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010350Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010400Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010454Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010527Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010587Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010646Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010694Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010741Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010824Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010885Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.010957Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011010Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011151Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011209Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011261Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:50:46.011309Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.913739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:51:16.928436Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.928820Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.929034Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.929146Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.930407Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.935591Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.937225Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.938231Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.938917Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.939700Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.942957Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.944574Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.944734Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.944830Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.944912Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.945061Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.945136Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.945213Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.945272Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.946077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.946165Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.946226Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.947028Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.947158Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.947413Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948029Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948139Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948205Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948254Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948317Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948367Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.948414Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.951329Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.951519Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T13:51:16.951591Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.278714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:11:21.284038Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.286151Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.303611Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.303819Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.303895Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.303958Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304016Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304065Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304115Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304164Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304208Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304256Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304309Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304390Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304447Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304507Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304561Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304620Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304671Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304715Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304759Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304852Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.304969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305054Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305118Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305193Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305274Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305356Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305421Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305488Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305625Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305698Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305746Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.305821Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:11:21.306047Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.340613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:16.349891Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.351803Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.351932Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.352000Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.352051Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.355665Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.356183Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.356517Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.356642Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.356797Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.356941Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357126Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357201Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357313Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357425Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357637Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357712Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357791Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357853Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357903Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.357957Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358009Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358063Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358181Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358352Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358454Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358547Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358656Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358759Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358875Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.358962Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.359146Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.361989Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.362117Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:16.362183Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.211859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:47.218105Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.218383Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.218481Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.218554Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.218616Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.218680Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.219960Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.221710Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.221867Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.221948Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222028Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222092Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222158Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222253Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222316Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222365Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222412Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222466Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222522Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222568Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222610Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.222656Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224700Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224765Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224809Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224851Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224893Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224935Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.224977Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.225018Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.225087Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.225128Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.227361Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.227452Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:27:47.227504Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.541609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:43:42.553142Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553308Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553375Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553442Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553501Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553557Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553622Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553725Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553771Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553817Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.553861Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.554537Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.554637Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.554703Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.554763Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.555082Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.555146Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.557572Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.558135Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559002Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559075Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559124Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559169Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559212Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559281Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559326Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559369Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559413Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559456Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559513Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559560Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559603Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559645Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559687Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:43:42.559732Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.883884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:56:30.892048Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.893641Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.893753Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.893820Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.893937Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.894030Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.894085Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.896495Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897220Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897342Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897419Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897498Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897571Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.897794Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.898028Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.898140Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.898222Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901415Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901603Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901702Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901779Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901864Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.901944Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902020Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902102Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902195Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902311Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902396Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902474Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902590Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902672Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902775Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902851Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902906Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:56:30.902953Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.740846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:57:01.746608Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.746789Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.747055Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.747806Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.747897Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748044Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748220Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748301Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748370Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748418Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.748526Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.750943Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.751113Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.751218Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.751376Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.751481Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.756417Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.756573Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.757807Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.759768Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.759869Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.759954Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760054Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760120Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760185Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760252Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760304Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760352Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760396Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760455Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760506Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760580Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760657Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760723Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T14:57:01.760789Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.541869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:12:56.559461Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.561302Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.561603Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.561846Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.562268Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.564260Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.564401Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.564462Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.564517Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.564751Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.565004Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.566414Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.568136Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.569421Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.570436Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.570769Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.571036Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.571487Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.571932Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.574473Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575007Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575259Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575454Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575585Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575658Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575728Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.575847Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.576297Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.576521Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.576694Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.576790Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.577076Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.577190Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.577330Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:12:56.577525Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.099641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:28:52.110982Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.111800Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.112548Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.112884Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.113431Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.114595Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.114873Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.115657Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.116274Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.116785Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117496Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117599Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117787Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117866Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117925Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.117993Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.118091Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.125650Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.126957Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.127575Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.128322Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.128647Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.128981Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.129544Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.130626Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.131601Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.131719Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.131818Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.131927Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132010Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132082Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132160Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132250Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132327Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:28:52.132408Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:22.999190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:29:23.007937Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.013909Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.014906Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015173Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015290Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015386Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015457Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015526Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015598Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015659Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015766Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015865Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.015952Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016028Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016097Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016157Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016204Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016253Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016331Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016429Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016505Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016560Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016655Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016704Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016752Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016800Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016850Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016895Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.016941Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017070Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017151Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017206Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017273Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017330Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:29:23.017390Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.662680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:17.668673Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.668899Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.675660Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.676520Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.677519Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678503Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678590Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678646Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678709Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678766Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678813Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678860Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678915Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.678979Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679048Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679136Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679189Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679241Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679289Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679341Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679392Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679436Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679486Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679550Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679625Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.679676Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682498Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682617Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682678Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682741Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682793Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682837Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682883Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682928Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:17.682972Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.796775","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:48.815170Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.815661Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.815949Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.818164Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.818498Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.818615Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.818693Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.818873Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819139Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819406Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819574Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819674Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819826Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.819899Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820305Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820388Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820455Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820510Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820566Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820622Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820684Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820752Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820826Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820891Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.820946Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821001Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821051Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821105Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821161Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821225Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821276Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821324Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821369Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821414Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:45:48.821957Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.451129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:57:55.459271Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.459986Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.460160Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.462641Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.465227Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466182Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466275Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466349Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466424Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466485Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466538Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466587Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466633Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466694Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466788Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466840Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.466883Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471453Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471643Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471716Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471783Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471843Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471894Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471942Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.471991Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472036Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472083Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472130Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472202Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472269Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472330Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472389Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472444Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472537Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T15:57:55.472601Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.346617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:13:38.351403Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.352410Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357099Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357300Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357379Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357439Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357507Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357566Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357623Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357684Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357751Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357815Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357873Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357918Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.357963Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358013Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358060Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358120Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358173Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358216Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358259Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358303Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358346Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358388Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358433Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358479Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358553Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358600Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358642Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358683Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358727Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.358769Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.362190Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.362330Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:13:38.362393Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.213572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:29:34.221231Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.223750Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224104Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224267Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224413Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224553Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224641Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224721Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224786Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224847Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224893Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224943Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.224999Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225066Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225145Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225204Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225277Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225345Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225423Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225535Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225601Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225664Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225716Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225767Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225815Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225877Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225933Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.225977Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226022Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226068Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226116Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226166Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226213Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226261Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:29:34.226304Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.778881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:30:04.788536Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.789177Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.789273Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.789388Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.792105Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793049Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793181Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793264Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793322Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793368Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793423Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793470Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793514Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793582Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793626Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793669Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.793715Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.796769Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.796931Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797046Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797128Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797183Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797236Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797283Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797330Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797378Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797431Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797480Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797528Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797596Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797651Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797699Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797778Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797838Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:30:04.797884Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.983218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:45:58.989025Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.989238Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.989840Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.990098Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.992015Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.992967Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.993894Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994017Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994079Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994138Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994183Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994240Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994287Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994334Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994580Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994662Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.994734Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997390Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997540Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997642Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997718Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997792Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997864Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997932Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.997979Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998045Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998140Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998243Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998342Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998415Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998484Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998548Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998625Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998705Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:45:58.998771Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.696504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:58:31.703163Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.703630Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.703966Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704047Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704103Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704152Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704204Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704264Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704402Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.704634Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.707759Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.707883Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.707939Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.707991Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.708061Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.708127Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.708205Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.711397Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.711643Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.711750Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.711830Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.711967Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712082Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712167Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712291Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712375Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712476Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712555Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712753Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.712887Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.713060Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.713173Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.713262Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.713370Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:58:31.713495Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.844554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:59:02.851391Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.853411Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860262Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860457Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860543Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860604Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860667Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860723Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860776Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860830Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860873Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860925Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.860987Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861049Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861131Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861218Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861281Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861333Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.861381Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.862545Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.863298Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.863492Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864180Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864291Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864443Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864513Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864592Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864671Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864742Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864841Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.864924Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.865002Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.865079Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.865152Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-23T16:59:02.865234Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
