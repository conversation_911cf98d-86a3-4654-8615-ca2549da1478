{"timestamp":"2025-07-23T00:10:55.146257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:10:55.159026","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T00:11:25.418386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:11:25.433524","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T00:27:20.853354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:27:20.872888","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T00:43:15.923319","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:15.931573","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T00:43:46.292997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:46.303720","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T00:59:41.754223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:59:41.762370","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T01:00:12.510927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:00:12.522468","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T01:16:07.045030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:16:07.054375","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T01:32:02.100833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:02.112886","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T01:32:32.865868","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:32.876003","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T01:48:27.741381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:48:27.749903","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:04:23.453641","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:23.469803","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:04:54.436151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:54.444374","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:20:48.812799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:20:48.822981","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:36:44.288440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:36:44.297659","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:37:15.172530","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:37:15.181283","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T02:53:09.821267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:53:09.831452","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:07:52.941320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:07:52.948581","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:08:23.874727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:08:23.883424","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:24:19.382657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:24:19.392453","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:40:14.784462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:14.794527","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:40:45.600580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:45.616701","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T03:56:39.971661","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:56:39.981082","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T04:12:35.792126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:12:35.802232","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T04:13:06.622879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:13:06.638250","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T04:29:02.125392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:29:02.134853","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T04:44:57.598238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:44:57.607970","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T04:45:28.554197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:45:28.563001","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:04:05.999041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:06.010078","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:04:37.123198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:37.133812","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:20:31.028429","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:20:31.036001","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:21:02.216770","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:21:02.228368","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:36:56.706360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:36:56.722723","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:52:52.437981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:52:52.464540","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T05:53:22.763367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:53:22.779244","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:09:18.238357","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:09:18.245924","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:25:13.657003","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:13.667718","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:25:44.688675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:44.698515","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:41:38.584305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:41:38.592025","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:49:33.339202","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:49:33.349221","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T06:50:03.461682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:50:03.471943","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:05:41.422191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:05:41.432377","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:21:36.070594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:21:36.078539","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:22:06.992855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:22:07.007762","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:37:49.519980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:37:49.537114","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:50:25.353954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:25.361021","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T07:50:55.894658","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:55.903911","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:06:50.451950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:06:50.464862","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:07:21.275727","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:07:21.286742","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:23:10.809007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:23:10.820633","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:39:06.788844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:06.802347","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:39:37.600302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:37.609513","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T08:51:41.815551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:51:41.824640","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:07:36.648804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:07:36.657359","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:08:07.584368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:08:07.595005","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:24:01.973030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:01.981552","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:24:32.859548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:32.871851","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:40:27.547650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:40:27.555548","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:52:39.382314","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:52:39.397146","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T09:53:10.515406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:53:10.523794","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:09:04.567771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:09:04.579242","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:24:59.361316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:24:59.374524","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:25:30.289990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:25:30.298582","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:41:24.437061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:41:24.454990","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:53:28.442226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:28.452310","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T10:53:59.257323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:59.266433","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T11:09:53.818840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:09:53.828518","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T11:20:50.231639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:20:50.245133","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T11:21:21.379095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:21:21.387805","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T11:53:22.943784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:53:23.027773","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:09:16.912455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:16.938174","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:09:48.019826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:48.029531","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:25:17.213110","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:25:17.230801","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:41:12.736327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:12.744234","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:41:43.723431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:43.736525","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:54:38.985562","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:54:38.994077","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T12:55:10.093989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:55:10.152150","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:11:04.119738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:11:04.128987","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:14:53.757217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:14:53.767586","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:30:21.816215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:21.824958","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:30:52.696179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:52.707840","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:31:23.658327","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:23.668452","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:31:54.606539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:54.621730","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:32:25.582129","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:25.594177","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:32:58.133237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:58.170751","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:50:46.070182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:50:46.084874","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T13:51:17.007269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:51:17.027264","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:11:21.364651","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:11:21.374176","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:27:16.405156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:16.420298","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:27:47.276843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:47.284961","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:43:42.617081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:43:42.626767","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:56:30.961607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:56:30.970410","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T14:57:01.807560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:57:01.818666","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:12:56.634233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:12:56.662361","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:28:52.183548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:28:52.194647","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:29:23.067197","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:29:23.079410","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:45:17.729642","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:17.748490","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:45:48.886856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:48.902279","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T15:57:55.517604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:57:55.525918","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:13:38.401655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:13:38.409265","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:29:34.272693","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:29:34.282086","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:30:04.841783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:30:04.849974","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:45:59.044034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:45:59.053962","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:58:31.764183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:58:31.773377","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T16:59:02.923175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:59:02.931307","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T17:14:57.608718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:14:57.619395","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T17:30:41.033766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:30:41.041674","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T17:31:12.137101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:31:12.147465","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T17:47:05.845386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:47:05.867233","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T17:59:28.923911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:59:28.932887","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:00:00.262004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:00:00.275432","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:15:52.187933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:15:52.198219","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:30:46.585582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:30:46.638208","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:31:17.284337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:31:17.306958","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:31:48.145041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:31:48.156438","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:32:18.861909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:32:18.870939","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:32:32.160927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:32:32.337856","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:33:03.367093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:33:03.386724","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:33:34.239898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:33:34.253705","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:34:05.035652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:34:05.056377","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:34:35.980097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:34:35.996919","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:35:07.248894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:35:07.288500","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:35:37.583002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:35:37.599583","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:36:08.534697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:36:08.553307","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:36:39.267881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:36:39.286648","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:37:10.071418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:37:10.085505","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:37:40.986995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:37:40.998759","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:38:11.832318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:38:11.901763","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:38:42.688346","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:38:42.709126","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:39:13.515138","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:39:13.531842","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:39:44.359563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:39:44.373892","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:40:15.442688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:40:15.458424","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:40:46.271445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:40:46.285836","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:41:16.977575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:41:16.994139","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:41:47.693636","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:41:47.707252","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:42:18.556993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:42:18.571888","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:42:49.448283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:42:49.464930","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:43:20.247041","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:43:20.266192","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:43:51.022898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:43:51.035198","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:44:21.853104","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:44:21.866736","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:44:52.712673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:44:52.728777","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:45:23.508762","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:45:23.527638","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:45:54.436262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:45:54.450096","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:46:25.225945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:46:25.239029","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:46:56.002792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:46:56.016829","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:47:26.864683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:47:26.883308","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:47:57.683485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:47:57.695418","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:48:28.475065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:48:28.488798","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:48:59.356763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:48:59.372345","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:49:30.189757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:49:30.207292","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:50:01.334405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:50:01.393544","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:50:32.142394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:50:32.158355","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:51:02.935698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:51:02.959051","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:51:33.799565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:51:33.813476","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:52:04.744967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:52:04.764629","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:52:35.559881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:52:35.572617","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:53:06.364957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:53:06.380810","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:53:37.240891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:53:37.256875","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:54:08.058293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:54:08.072640","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:54:38.962360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:54:38.979660","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:55:09.714354","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:55:09.731259","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:55:40.537937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:55:40.550588","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:56:11.348438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:56:11.365250","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:56:42.424584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:56:42.490578","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:57:13.671052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:57:13.693320","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:57:44.091127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:57:44.136082","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:58:15.151274","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:58:15.170572","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:58:46.123631","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:58:46.140711","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:59:17.073810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:59:17.091493","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T18:59:47.373103","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:59:47.390250","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:00:18.230078","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:00:18.243550","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:00:49.375195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:00:49.393002","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:01:20.279384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:01:20.296512","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:01:50.588180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:01:50.605946","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:02:20.873791","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:02:20.897551","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:02:51.832075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:02:51.846933","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:03:22.765913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:03:22.779935","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:03:53.643463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:03:53.658685","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:04:24.490095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:04:24.512625","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:04:55.312453","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:04:55.384081","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:05:26.246846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:05:26.268209","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:05:57.184435","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:05:57.210863","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:06:28.042627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:06:28.065616","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:06:59.118486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:06:59.143869","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:07:30.039750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:07:30.054972","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:08:00.870896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:08:00.892045","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:08:31.668739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:08:31.694985","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:09:02.659847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:09:02.680666","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:09:33.560298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:09:33.574811","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:10:04.470008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:10:04.483660","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:10:35.353345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:10:35.371207","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:11:06.285932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:11:06.306663","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:11:37.081228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:11:37.095972","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:12:07.987175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:12:08.008639","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:12:38.964617","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:12:38.979296","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:13:09.751037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:13:09.765721","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:13:40.635843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:13:40.648229","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:14:11.471061","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:14:11.492026","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:14:42.285305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:14:42.301159","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:15:13.065930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:15:13.084889","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:15:43.857822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:15:43.871667","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:16:14.722871","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:16:14.736054","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:16:45.430734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:16:45.444543","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:17:16.189608","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:17:16.203204","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:17:46.961211","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:17:46.977986","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:18:17.797035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:18:17.815485","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:18:48.627603","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:18:48.647195","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:19:19.286250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:19:19.297637","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:19:49.872182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:19:49.883679","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:20:20.554829","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:20:20.566674","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:20:51.199835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:20:51.209550","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:21:21.861993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:21:21.871354","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:21:52.641237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:21:52.660257","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:22:23.223943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:22:23.235173","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:22:53.937485","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:22:53.948911","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:33:56.163915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:33:56.181790","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:34:26.849374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:34:26.862294","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:35:45.089068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:35:45.135613","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:36:15.662878","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:36:15.686315","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:36:46.444637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:36:46.461916","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:37:17.135146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:37:17.149219","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:37:47.819020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:37:47.836590","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:38:18.583839","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:38:18.601074","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:38:49.575639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:38:49.593991","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:39:20.402930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:39:20.417485","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:39:51.158622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:39:51.171729","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:40:21.904648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:40:21.917545","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:40:52.659172","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:40:52.671919","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:41:23.355725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:41:23.371560","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:41:54.009732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:41:54.021742","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:42:24.706105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:42:24.722049","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:42:55.384099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:42:55.399002","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:43:26.003973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:43:26.016747","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:43:56.751621","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:43:56.764461","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:44:27.419086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:44:27.433971","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:44:58.191146","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:44:58.205356","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T19:45:29.039167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T19:45:29.056298","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:01:11.271066","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:01:11.289877","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:01:41.926097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:01:41.944708","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:17:36.268449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:17:36.283820","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:33:32.650494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:33:32.668743","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:34:03.349571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:34:03.366766","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T20:49:57.659167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T20:49:57.675414","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:05:54.077768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:05:54.110284","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:06:24.874699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:06:24.894172","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:22:19.272190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:22:19.287937","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:22:50.014422","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:22:50.093159","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:38:44.665439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:38:44.678616","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T21:46:39.667942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T21:46:39.680149","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:02:35.122720","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:02:35.140913","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:03:05.819685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:03:05.834531","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:19:01.238606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:19:01.256794","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:34:44.589416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:34:44.613481","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:35:15.300680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:35:15.317126","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:51:10.429750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:51:10.446448","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T22:51:41.124664","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T22:51:41.147952","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:07:17.672814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:07:17.690527","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:23:13.425178","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:23:13.450524","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:23:44.104452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:23:44.126500","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:39:38.369722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:39:38.382665","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:40:09.023394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:40:09.256225","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-23T23:55:20.495735","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T23:55:20.509734","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
