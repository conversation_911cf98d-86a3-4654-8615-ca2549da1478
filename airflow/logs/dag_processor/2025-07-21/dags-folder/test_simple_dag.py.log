{"timestamp":"2025-07-21T00:04:46.988700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:04:47.008830","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:05:17.608459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:05:17.619076","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:21:13.844745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:21:13.868301","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:21:44.644384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:21:44.664329","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:37:38.991408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:37:39.010772","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:53:35.387843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:53:35.408996","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T00:54:06.203622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:54:06.220272","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:09:59.795803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:09:59.811998","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:25:55.202358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:25:55.225175","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:26:25.843893","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:26:25.860554","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:42:21.180736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:42:21.203547","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:58:16.443734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:58:16.465575","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T01:58:47.120511","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:58:47.134357","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T02:14:42.451180","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:14:42.468879","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T02:30:17.759958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:30:17.778584","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T02:30:48.408037","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:30:48.420959","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T02:46:43.934065","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:46:43.953403","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:02:39.285134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:02:39.302842","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:03:10.024214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:03:10.053928","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:19:05.412405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:19:05.430729","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:19:36.285657","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:19:36.309112","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:35:30.741866","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:35:30.761118","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:50:59.493785","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:50:59.518324","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T03:51:30.329390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:51:30.353626","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T04:07:23.864852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:07:23.884707","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T04:23:20.252865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:23:20.270085","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T04:23:50.889835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:23:50.908867","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T04:56:02.221897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:56:02.241752","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:09:51.394944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:09:51.539814","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:25:46.108069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:25:46.125463","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:26:16.910224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:26:16.934069","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:42:12.440193","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:42:12.460174","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:58:07.086649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:58:07.108274","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T05:58:37.682683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:58:37.707786","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:14:33.051622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:14:33.068283","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:30:28.537467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:30:28.569764","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:30:59.299032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:30:59.316634","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:42:15.864318","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:42:15.887703","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:58:12.142766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:58:12.160693","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T06:58:42.890881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:58:42.904286","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:10:53.919501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:10:53.936582","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:26:44.259349","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:26:44.278834","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:27:14.842139","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:27:14.858953","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:43:10.351179","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:43:10.369619","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:59:05.905689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:59:05.921985","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T07:59:36.636582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:59:36.658872","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:11:52.804245","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:11:52.821197","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:27:49.042620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:27:49.060822","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:28:19.708439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:28:19.725008","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:41:58.001797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:41:58.115568","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:45:12.877743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:45:12.981742","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:45:46.735291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:45:46.837295","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:46:17.690820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:46:17.723222","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:46:48.336718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:46:48.356695","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:47:19.070345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:47:19.088891","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:47:49.784293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:47:49.803918","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:48:20.429950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:48:20.455663","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:48:51.241895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:48:51.258318","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:49:22.217191","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:49:22.233261","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T08:49:53.325876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:49:53.351054","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T19:36:28.749853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:36:28.805060","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T19:37:02.772276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:37:02.809967","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T19:37:33.432177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:37:33.456711","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T19:38:04.173334","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:38:04.196256","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-21T19:38:16.874717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:39:11.994844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:39:48.501183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:40:19.195706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:40:49.961137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:41:20.748209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:41:51.651514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:42:22.392301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:42:53.134106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:43:24.459373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:49:51.009942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:50:24.030384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:50:54.832516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:51:25.210613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:51:57.050802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:52:29.874276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:53:00.551711","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T20:01:38.854993","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T21:25:25.884602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:07:47.565012","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:23:42.377452","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:39:38.050799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:40:08.752682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:56:03.346288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:11:58.068977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:12:28.980519","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:28:24.437937","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:44:20.224272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:44:51.357415","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
