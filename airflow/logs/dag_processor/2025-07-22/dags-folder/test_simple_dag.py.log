{"timestamp":"2025-07-22T00:00:45.987669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T00:16:41.669903","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T00:17:12.779374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T00:33:07.258335","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T00:49:02.791596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T00:49:33.653741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:05:28.315687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:21:23.034289","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:21:53.785870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:37:43.085048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:43:18.051194","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:43:48.929609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T01:59:43.554004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T02:15:39.122757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T02:16:09.771131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T02:28:58.816114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T02:44:54.414312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T02:45:25.353133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:01:19.072611","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:17:01.104167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:17:32.267821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:33:27.085560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:44:17.309988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T03:44:48.170031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:00:43.754222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:16:26.382454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:16:57.286826","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:32:51.113489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:33:22.011742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T04:45:43.386277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:01:38.802854","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:02:09.574941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:18:04.198645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:33:59.790632","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:34:30.606848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T05:46:40.425620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:02:35.848739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:03:06.750703","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:18:53.060728","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:34:48.742034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:35:19.670320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:47:29.793743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T06:48:00.852208","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:03:55.319374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:19:50.942107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:20:21.867756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:36:16.009095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:48:26.327100","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T07:48:56.532592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:04:51.434067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:20:46.987853","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:21:17.988255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:36:58.608503","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:49:31.322358","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T08:50:02.183968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:05:35.785457","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:21:31.455442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:22:02.392222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:37:56.988282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:50:29.001739","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T09:51:00.162607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:06:54.491323","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:22:50.273206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:23:21.264228","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:39:15.891561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:39:46.754763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T10:51:40.637656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:07:36.397014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:08:07.190585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:23:49.892640","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:39:45.569656","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:40:16.728515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T11:52:41.772345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:08:38.166105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:09:08.973412","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:25:03.900394","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:40:59.384605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:41:30.208626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T12:53:39.205969","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:09:35.587928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:10:06.698974","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:26:01.347684","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:26:32.436153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:42:26.986487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:54:29.803043","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T13:55:00.842368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:10:55.477237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:26:47.051112","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:27:18.090928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:43:11.897315","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:55:21.752038","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T14:55:52.767825","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:11:37.541462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:12:08.462514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:28:03.302506","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:43:59.057111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:44:30.206892","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T15:56:49.480052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:12:44.785040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:13:15.739940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:29:10.644985","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:45:06.329599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:45:37.541388","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T16:57:44.572898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:13:35.806828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:14:06.790998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:30:01.410209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:45:57.007876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:46:27.893915","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T17:58:35.805397","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:14:26.568678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:14:56.894316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:30:52.348014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:31:23.254759","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:47:17.914051","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:54:48.370387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T18:55:19.191492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:15:09.183639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:15:40.156448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:31:34.006823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:47:29.594294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:48:00.699882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T19:53:51.933579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:00:24.798795","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:00:56.010655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:16:32.057466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:32:26.975118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:32:57.238301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:48:52.678277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T20:49:23.774729","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:01:46.007296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:17:41.665645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:18:12.813464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:34:07.297954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:50:02.715653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T21:50:33.636678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:06:28.454876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:06:59.285478","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:22:54.855248","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:38:50.507643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:54:46.099443","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T22:55:16.991712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:11:11.704386","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:11:42.580074","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:27:38.096002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:43:33.798599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:44:04.786654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-22T23:55:00.098883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
