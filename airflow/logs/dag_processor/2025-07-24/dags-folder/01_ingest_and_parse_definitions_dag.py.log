{"timestamp":"2025-07-24T00:11:15.710207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:11:15.728603","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T00:11:46.342420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:11:46.362705","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T00:27:41.661157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:27:41.679503","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T00:43:37.139786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:43:37.159417","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T00:44:07.863699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T00:44:07.880573","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:00:02.420497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:00:02.439282","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:15:57.699402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:15:57.714710","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:16:28.396537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:16:28.419886","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:32:22.842089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:32:22.856139","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:32:53.575417","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:32:53.591502","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T01:48:48.971584","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T01:48:48.992691","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:04:44.448236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:04:44.461288","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:05:15.119673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:05:15.133752","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:21:00.970555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:21:00.990999","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:36:56.316439","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:36:56.338970","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:37:27.115368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:37:27.138420","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T02:53:22.503722","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T02:53:22.551222","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T03:09:17.924434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:09:17.940536","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T03:09:48.582815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:09:48.606147","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T03:25:43.197625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:25:43.211080","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T03:41:26.633481","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:41:26.654555","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T03:41:57.247380","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T03:41:57.260853","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:02:30.178090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:02:30.204782","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:03:00.881236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:03:00.897948","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:18:55.229053","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:18:55.241584","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:34:50.663705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:34:50.680201","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:35:21.257698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:35:21.272009","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T04:51:16.556586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T04:51:16.568417","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:07:12.982408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:07:12.996403","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:07:43.680666","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:07:43.697429","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:23:38.039552","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:23:38.058123","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:29:31.209244","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:29:31.226994","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:30:01.985305","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:30:02.001550","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T05:45:55.713983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T05:45:55.733578","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:01:37.624059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:01:37.647174","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:02:08.328210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:02:08.356982","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:18:03.804433","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:18:03.825668","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:33:58.372173","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:33:58.386910","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:34:29.083364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:34:29.104744","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T06:50:24.537054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T06:50:24.562277","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:06:19.933472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:06:19.948227","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:06:50.599398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:06:50.617711","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:22:45.992418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:22:46.011307","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:30:25.676024","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:30:25.690209","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:30:56.373930","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:30:56.392460","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T07:46:51.843920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T07:46:51.860339","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:02:47.133556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:02:47.147186","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:03:17.827852","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:03:17.852721","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:19:13.417882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:19:13.435498","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:19:44.245579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:19:44.269021","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:22:06.924609","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:22:06.971043","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:22:37.729368","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:22:37.747044","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:23:08.412529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:23:08.429016","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:23:39.086548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:23:39.105812","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:24:09.729574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:24:09.747385","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:24:40.420564","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:24:40.435794","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:25:11.117848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:25:11.139366","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:25:41.737745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:25:41.752774","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:26:12.354614","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:26:12.381035","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:26:43.011600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:26:43.047021","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:27:13.825472","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:27:13.854712","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:27:44.623786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:27:44.640972","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:28:15.259475","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:28:15.273629","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:28:45.978345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:28:46.002035","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:29:16.673456","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:29:16.694704","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:29:47.343783","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:29:47.359998","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:30:18.028255","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:30:18.046376","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:30:48.730873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:30:48.748729","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:31:19.457694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:31:19.472639","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:31:50.167605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:31:50.182219","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:32:21.144507","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:32:21.164108","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:32:51.875251","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:32:51.890356","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:33:22.984395","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:33:23.001679","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:34:00.081896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:34:00.099288","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:34:30.727018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:34:30.750950","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:35:01.478717","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:35:01.503821","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:35:32.297451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:35:32.321093","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:36:02.981982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:36:02.997470","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:36:33.700980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:36:33.721523","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:37:04.286157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:37:04.302034","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:37:34.948706","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:37:34.973191","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:38:05.605034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:38:05.623548","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:38:36.225011","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:38:36.244689","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:39:06.894524","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:39:06.909048","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:39:37.577010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:39:37.593228","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:40:08.317391","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:40:08.332863","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:40:38.982263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:40:38.994843","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:41:09.682654","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:41:09.699751","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:41:40.376438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:41:40.389574","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:42:11.151268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:42:11.165959","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:42:41.856945","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:42:41.879514","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:43:12.619803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:43:12.636777","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:43:43.310402","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:43:43.328710","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:44:14.020375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:44:14.037568","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:44:44.745931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:44:44.761812","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:45:15.424714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:45:15.440772","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:45:46.059800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:45:46.079369","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:46:16.742855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:46:16.761701","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:46:47.560634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:46:47.578478","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:47:18.143492","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:47:18.157774","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:47:48.847628","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:47:48.863169","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:48:19.530807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:48:19.546522","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:48:50.157227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:48:50.170630","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:49:20.818091","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:49:20.843507","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:49:51.473153","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:49:51.489634","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:50:22.095574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:50:22.117820","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:50:52.651371","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:50:52.664589","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:51:23.345409","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:51:23.366629","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:51:54.006595","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:51:54.022481","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:52:24.631463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:52:24.644960","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:52:55.313908","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:52:55.334131","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:53:25.927662","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:53:25.945331","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:53:57.345515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:53:58.781241","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:56:47.226333","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:56:47.300547","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:57:18.336214","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:57:18.353309","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:57:48.924660","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:57:48.942752","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:58:19.697243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:58:19.715304","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:58:50.840716","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:58:50.861122","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:59:21.642748","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:59:21.658837","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T08:59:52.340111","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T08:59:52.352699","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:00:22.924872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:00:22.941030","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:00:53.549375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:00:53.563739","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:01:24.146219","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:01:24.163026","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:01:54.782405","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:01:54.803303","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:02:25.480997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:02:25.492641","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:02:56.136350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:02:56.153773","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:03:26.828742","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:03:26.850564","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:03:57.488375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:03:57.505831","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:04:28.143040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:04:28.167307","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:04:58.791375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:04:58.810472","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:05:29.467499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:05:29.478973","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:06:44.439227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:06:44.604347","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:07:18.411489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:07:18.432017","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:07:49.133226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:07:49.153960","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:08:19.781133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:08:19.801825","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:08:50.556167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:08:50.574096","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:09:21.752741","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:09:21.803849","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:09:52.843326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:09:52.869339","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:10:06.528708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:10:06.596034","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared_path'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:10:36.800626","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:10:36.819516","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared_path'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:30:17.286855","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:30:17.415788","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:30:49.984271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:30:50.050139","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:31:20.815514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:31:20.864602","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:31:51.653280","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:31:51.692878","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:32:22.495440","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:32:22.545199","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:32:52.835419","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:32:52.908352","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-24T09:33:23.884644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-24T09:33:23.983825","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'xmlschema'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"},{"filename":"/opt/airflow/shared/common/data_parsers.py","lineno":5,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
