{"timestamp":"2025-07-20T00:00:27.818807","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:00:27.839461","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:08:25.719058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:08:25.743361","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:24:20.431946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:24:20.451987","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:24:51.183653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:24:51.198829","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:40:37.021634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:40:37.036422","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:56:33.448589","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:56:33.465236","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T00:57:04.125548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T00:57:04.141077","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T01:12:58.361431","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:12:58.374094","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T01:28:45.478374","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:28:45.495329","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T01:29:16.166873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:29:16.181035","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T01:45:09.838649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T01:45:09.862314","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:01:04.573048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:01:04.592107","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:01:35.098980","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:01:35.111231","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:17:30.513597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:17:30.528681","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:33:25.155834","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:33:25.170066","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:33:55.899056","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:33:55.916474","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T02:49:50.609682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T02:49:50.630964","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:05:45.958321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:05:45.979261","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:06:16.737155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:06:16.754856","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:22:11.441156","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:22:11.455514","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:38:06.862913","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:38:06.881620","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:38:37.444559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:38:37.461493","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T03:54:32.866708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T03:54:32.884447","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:04:48.920995","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:04:48.957915","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:05:19.622655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:05:19.643122","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:21:15.077114","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:21:15.096851","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:37:10.621345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:37:10.648830","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:37:41.335381","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:37:41.355591","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T04:53:34.793957","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T04:53:34.817375","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:09:30.389966","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:09:30.407727","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:10:01.084340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:10:01.108849","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:25:54.591644","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:25:54.607008","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:26:25.274014","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:26:25.286943","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:42:20.599392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:42:20.616259","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:58:00.551207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:58:00.577465","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T05:58:31.287484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T05:58:31.310224","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T06:20:55.357223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:20:55.378174","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T06:21:26.141215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:21:26.162031","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T06:37:19.844884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:37:19.862937","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T06:53:16.080406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:53:16.098669","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T06:53:46.834095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T06:53:46.854980","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:06:53.341531","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:06:53.362562","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:07:24.096607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:07:24.338372","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:23:19.804679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:23:19.817457","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:39:14.352923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:39:14.371666","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:55:09.015195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:55:09.031938","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T07:55:39.785035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T07:55:39.804585","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:07:53.267559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:07:53.284122","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:23:48.681510","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:23:48.713480","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:24:19.342133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:24:19.356565","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:40:14.780557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:40:14.802261","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:40:45.401870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:40:45.421743","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T08:56:39.885736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T08:56:39.903118","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:08:49.526423","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:08:49.543657","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:09:20.270982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:09:20.314759","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:25:13.881022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:25:13.900688","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:41:10.301476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:41:10.320669","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:41:40.992206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:41:41.008600","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T09:57:35.488018","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T09:57:35.502108","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:09:44.072753","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:09:44.091412","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:10:14.857713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:10:14.872502","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:26:10.214847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:26:10.235249","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:42:05.621669","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:42:05.641335","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:42:36.211416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:42:36.245776","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:58:31.728712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:58:31.752756","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T10:59:02.386523","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T10:59:02.409729","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T11:11:00.573758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:11:00.588390","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T11:26:55.153498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:26:55.183957","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T11:27:25.804265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:27:25.820193","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T11:43:21.185494","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:43:21.204869","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T11:59:15.950936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T11:59:15.975182","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T12:11:37.659316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:11:37.680391","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T12:12:08.350777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:12:08.366293","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T12:28:02.720911","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:28:02.743731","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T12:43:58.146025","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:43:58.164705","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T12:44:28.819699","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T12:44:28.840225","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:00:24.116820","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:00:24.141642","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:00:54.875320","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:00:54.900263","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:13:05.715035","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:13:05.735246","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:29:03.042118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:29:03.062759","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:29:33.697574","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:29:33.725248","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T13:45:27.525863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T13:45:27.557271","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:01:22.999282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:01:23.057162","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:01:53.842898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:01:53.857711","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:13:53.064227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:13:53.082310","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:15:54.150815","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:15:54.172061","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:31:49.507771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:31:49.523974","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:47:45.196844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:47:45.210631","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T14:48:15.811745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T14:48:15.826245","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:04:10.395764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:04:10.410101","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:14:45.381019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:14:45.401081","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:15:16.062894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:15:16.079502","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:31:11.633001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:31:11.653753","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:47:06.942680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:47:06.956011","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T15:47:37.651983","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T15:47:37.668488","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:03:19.455184","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:03:19.469991","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:15:46.357975","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:15:46.376133","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:16:17.088990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:16:17.100584","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:32:12.393108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:32:12.409231","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:48:06.714605","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:48:06.735391","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T16:48:37.292005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T16:48:37.322104","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:04:32.726030","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:04:32.743495","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:16:43.994501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:16:44.016101","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:17:14.644126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:17:14.665380","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:33:00.930705","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:33:00.949882","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:33:31.653693","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:33:31.671039","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T17:49:26.192272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T17:49:26.208946","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:05:22.970375","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:05:23.047748","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:05:54.036344","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:05:54.064323","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:18:01.502416","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:18:01.520031","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:33:48.307535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:33:48.319836","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:34:18.988953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:34:19.002183","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:50:13.854792","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:50:13.886473","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T18:50:44.792525","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T18:50:44.826753","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:06:28.953873","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:06:28.974647","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:18:54.739588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:18:54.763500","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:19:25.426543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:19:25.449037","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:35:20.773867","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:35:20.804433","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:51:16.237309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:51:16.259405","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T19:51:46.978022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T19:51:46.999060","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:07:40.704137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:07:40.724577","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:19:47.689378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:19:47.708168","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:20:18.404278","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:20:18.421607","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:36:12.666543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:36:12.686718","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:36:43.319378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:36:43.335510","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T20:52:38.682167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T20:52:38.701472","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:08:34.112484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:08:34.133484","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:09:04.823430","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:09:04.838744","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:21:07.068687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:21:07.084825","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:36:36.316938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:36:36.327430","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:37:07.005293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:37:07.023203","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:53:00.587782","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:53:00.607418","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T21:53:31.527054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T21:53:31.552299","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T22:07:51.152313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T22:07:51.167329","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T22:23:46.634048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T22:23:46.668311","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T22:24:17.511260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T22:24:17.533376","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T22:43:38.633750","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T22:43:38.651520","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T22:44:09.370163","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T22:44:09.386952","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:00:03.800967","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:00:03.824131","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:16:00.245367","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:16:00.257113","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:16:30.971746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:16:30.987220","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:32:25.348888","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:32:25.371695","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:32:56.075311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:32:56.089743","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-20T23:48:51.643686","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-20T23:48:51.665097","level":"error","event":"Failed to import: /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"TypeError","exc_value":"DAG.__init__() got an unexpected keyword argument 'schedule_interval'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/test_simple_dag.py","lineno":34,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
