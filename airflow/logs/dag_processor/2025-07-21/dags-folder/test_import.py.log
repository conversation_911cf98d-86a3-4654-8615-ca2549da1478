{"timestamp":"2025-07-21T00:04:46.988700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:04:46.994844Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.995659Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.996643Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.996739Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.996808Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997344Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997635Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997716Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997771Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997819Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997863Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997908Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:46.997951Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.004850Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005068Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005164Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005231Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005295Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005364Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005433Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005489Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005538Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005585Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005634Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005680Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005733Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005778Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005824Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005869Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.005960Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.006003Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.006045Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.006111Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.006186Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:04:47.006262Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.607243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:05:17.614238Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614367Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614449Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614540Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614635Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614717Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614832Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.614930Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615016Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615126Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615214Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615309Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615403Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615482Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615556Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615648Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615743Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615848Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.615937Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616015Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616098Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616188Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616276Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616447Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616544Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616627Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616730Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616825Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616887Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.616944Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.617015Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.617100Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.617153Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.617202Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:05:17.617248Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.850099","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:21:13.867713Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868054Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868195Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868283Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868350Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868399Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868452Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868513Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868576Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868646Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868729Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868865Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.868948Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.869015Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.869077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.870042Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.872098Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.872882Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.872972Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873043Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873109Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873161Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873212Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873262Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873313Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873375Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873433Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873482Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873530Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873578Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873626Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873674Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873737Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873790Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:13.873838Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.644384","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:21:44.650149Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.650375Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.650624Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.651031Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.651916Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.652078Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.653766Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.654176Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.655847Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.657765Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.658037Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.658171Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.658469Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.658898Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.659090Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.659150Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662293Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662514Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662611Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662681Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662745Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662794Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662838Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662882Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.662960Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663032Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663096Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663144Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663190Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663234Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663289Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663351Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663399Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663452Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:21:44.663503Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:38.991418","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:37:38.999630Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.000084Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.000251Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.000718Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.000990Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.001106Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.001201Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.001296Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.002182Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.004560Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.004941Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.005257Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.006078Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.006180Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.006258Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.006305Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.006361Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.010196Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.011296Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.011507Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.011710Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.012375Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.013133Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.013412Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.014371Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015199Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015405Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015470Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015516Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015561Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015605Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015649Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015695Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015746Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:37:39.015831Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.387843","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:53:35.394180Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.394771Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.396033Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.399127Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.399363Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.399737Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.400000Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.400965Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.402150Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.402934Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.403499Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.403849Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.404820Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.404922Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.404983Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.405034Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.405083Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.408916Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.409500Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.409652Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.409781Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.410883Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413061Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413152Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413210Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413265Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413321Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413414Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413464Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413512Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413561Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413610Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413663Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413713Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:53:35.413761Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.203620","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T00:54:06.211335Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.212416Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.214509Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.214674Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.215331Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.215565Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.216405Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.217473Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.220387Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.221349Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.223294Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.223425Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.227979Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228173Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228252Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228363Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228452Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228533Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228603Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228672Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228734Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228849Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228918Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.228975Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229027Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229082Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229132Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229189Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229248Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229299Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229358Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229417Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229466Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229517Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T00:54:06.229565Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.795803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:09:59.805413Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.805979Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.806276Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.806394Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.807072Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.809827Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.810027Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811010Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811109Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811160Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811207Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811252Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811298Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811340Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811426Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811484Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.811535Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816308Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816479Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816551Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816600Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816647Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816716Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816764Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816807Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816849Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816895Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816936Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.816978Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817035Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817076Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817116Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817157Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817199Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:09:59.817240Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.202360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:25:55.231335Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231438Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231493Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231577Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231643Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231705Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231774Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231843Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231934Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.231979Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232041Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232122Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232261Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232356Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232432Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232512Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232584Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232662Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232728Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232790Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232835Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232882Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.232950Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233021Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233075Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233156Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233210Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233288Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233345Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233422Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233490Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233557Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233628Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233701Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:25:55.233764Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.843987","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:26:25.849296Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.849507Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.849776Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.849870Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.849930Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.849989Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850041Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850326Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850446Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850516Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850577Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.850620Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.851291Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.851468Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.852851Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.853782Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.853994Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.858448Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.859300Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.861173Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.861368Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.861487Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.861541Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.862587Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.863594Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.864585Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865328Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865622Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865686Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865732Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865778Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865823Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865911Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.865958Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:26:25.866006Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.180097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:42:21.192484Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.192658Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.192753Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.192844Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.192953Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193033Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193199Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193286Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193351Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193404Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193454Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193526Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193580Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193636Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193689Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193746Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.193815Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.198388Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.198775Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.198994Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.199184Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.199403Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.199707Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.200277Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201004Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201329Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201564Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201713Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201792Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.201861Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.202510Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.202585Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.202649Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.202772Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:42:21.202857Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.443732","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:58:16.451707Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.454421Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.454635Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.455205Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.455298Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.455463Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458369Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458598Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458672Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458735Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458835Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458892Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458938Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.458984Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.459029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.459073Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.459117Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.461626Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.461766Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.461868Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.461916Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.461963Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.462005Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.462049Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.462091Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.464351Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.464500Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.464583Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.464676Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.464883Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.465057Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.465211Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.465347Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.465460Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:16.465537Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.120518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T01:58:47.124852Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.124991Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.125194Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.125636Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.125848Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.125967Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.126037Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.126105Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.126173Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.127769Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.128691Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.128810Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.128906Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.129005Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.129092Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.129179Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.129262Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.136615Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.136850Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.136998Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.137123Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.138000Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.139002Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140007Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140367Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140427Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140472Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140516Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140558Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140601Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140643Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140690Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140733Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140780Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T01:58:47.140819Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.450240","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:14:42.459142Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.459342Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.460896Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.461849Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.462676Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.462995Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.464251Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.464865Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.465853Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.465952Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466023Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466080Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466157Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466223Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466278Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466338Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466396Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466451Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466514Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466559Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466600Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466641Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466682Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466724Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466764Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466811Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466853Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466894Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466936Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.466977Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.467020Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.467060Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.473243Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.473314Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:14:42.473370Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.759981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:30:17.771446Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.774458Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775358Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775518Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775605Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775671Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775740Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775798Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775852Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775919Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.775981Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776036Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776094Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776164Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776234Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776293Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776358Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776476Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776546Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776625Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776682Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776745Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776796Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776840Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776883Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776929Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.776976Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.777036Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.777089Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.777165Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.777213Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.777265Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.780840Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.781410Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:17.782426Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.408859","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:30:48.415910Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416343Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416480Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416560Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416634Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416728Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416806Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.416885Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.419610Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.419800Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.420867Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.421440Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.421589Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.421638Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.421743Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.422845Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.422926Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.426989Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427185Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427267Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427326Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427381Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427433Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427539Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427602Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427657Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427709Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427763Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427834Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427893Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.427945Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.428008Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.428068Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.428121Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:30:48.428181Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.934069","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T02:46:43.946694Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.947402Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.947611Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.950317Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.950980Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.952292Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.954114Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.954320Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.954464Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.955373Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.957775Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958013Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958215Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958416Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958516Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958612Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.958713Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.963579Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.963811Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964144Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964550Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964752Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964853Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964931Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.964993Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965053Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965125Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965211Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965295Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965380Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965467Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965636Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965724Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965811Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T02:46:43.965873Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.285134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:02:39.297373Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.297659Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.297989Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.306179Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.306975Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307152Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307266Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307343Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307411Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307481Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307567Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307626Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307684Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307729Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307785Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307849Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307916Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.307984Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308045Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308097Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308140Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308230Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308290Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308337Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308382Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308430Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308475Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308518Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308561Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308603Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308652Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308698Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308741Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308789Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:02:39.308831Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.024212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:03:10.034913Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.035418Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.036732Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.036902Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.038728Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039051Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039292Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039490Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039574Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039653Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039716Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039767Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.039824Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.040028Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.040473Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.040597Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.040763Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.040986Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041101Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041245Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041342Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041457Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041559Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041651Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041768Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041853Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.041952Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.042098Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.042193Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.042292Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.042465Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.042589Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.049003Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.049339Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:03:10.049840Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.411982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:19:05.421675Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.424185Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.424685Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.425348Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.425486Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.425733Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.425821Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428117Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428306Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428398Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428471Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428542Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428609Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428673Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428737Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428802Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.428865Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.431487Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.431612Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.433131Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.433294Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435193Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435401Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435484Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435541Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435588Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435635Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435706Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435750Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435794Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435842Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435885Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435930Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.435974Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:05.436018Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.285653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:19:36.297440Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300136Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300356Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300453Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300640Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300755Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300813Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300870Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300928Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.300978Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301022Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301095Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301154Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301199Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301250Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301313Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301365Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301434Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301491Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301545Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.301598Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303131Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303241Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303290Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303335Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303384Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303448Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303494Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303537Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303581Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303627Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303670Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303713Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303760Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:19:36.303804Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.741257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:35:30.748199Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.748744Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.749950Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.752821Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.752957Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753014Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753062Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753112Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753163Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753225Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753275Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753324Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753368Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753413Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753457Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753532Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.753589Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.756735Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.756944Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757028Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757092Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757150Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757206Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757282Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757330Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757383Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757435Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757490Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757551Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757600Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757645Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757693Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757739Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757789Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:35:30.757845Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.496907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:50:59.507789Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.509657Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.509815Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.509907Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510091Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510187Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510294Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510400Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510507Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510649Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510724Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510826Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510903Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.510975Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511096Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511159Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511220Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511303Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511357Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511403Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511452Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511505Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511558Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511606Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511678Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511743Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511795Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511842Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511888Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.511940Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.512020Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.512082Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.512131Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:50:59.512195Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.329390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T03:51:30.338857Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.341401Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.341832Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.342469Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.342607Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.342704Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.342820Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.342912Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.343001Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.343138Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.343258Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.345427Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.345717Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.346019Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.346222Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.346937Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.347076Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.350701Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.350890Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.350974Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351033Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351084Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351131Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351176Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351232Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351285Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351341Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351387Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351433Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351477Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351523Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351570Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351663Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351716Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T03:51:30.351760Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.864835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:07:23.870669Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.870847Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.871046Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.871473Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.873005Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874569Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874676Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874742Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874790Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874841Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.874970Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.875092Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.875150Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.875198Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.875269Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.875328Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.881483Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.881857Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.882121Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.882783Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.883135Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.883464Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.883767Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.883946Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884058Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884199Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884375Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884476Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884564Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884653Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884759Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884862Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.884955Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.885077Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:07:23.885171Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.252864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:23:20.269352Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269557Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269639Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269721Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269786Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269848Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269916Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.269982Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270038Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270101Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270186Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270255Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270314Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270363Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270431Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270490Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270539Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270646Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270778Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.270852Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.271744Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.272716Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.273734Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.273891Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.273959Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274011Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274055Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274099Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274142Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274184Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274228Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274271Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274314Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274357Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:20.274398Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.889835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:23:50.911676Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.911780Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.911831Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.911888Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.911955Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912025Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912081Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912149Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912207Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912252Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912295Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912352Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912417Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912500Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912554Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912610Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912672Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912738Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912794Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912838Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912882Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912927Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.912969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913013Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913055Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913110Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913164Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913236Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913300Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913362Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913407Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913451Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913496Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913547Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:23:50.913597Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.221897","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T04:56:02.236616Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.237545Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.238010Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.238937Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242342Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242472Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242528Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242618Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242669Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242715Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242759Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.242805Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248229Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248360Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248434Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248495Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248548Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248614Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248669Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248718Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248762Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248805Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248850Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248896Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248941Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.248987Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249032Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249075Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249119Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249163Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249206Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249250Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249295Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249340Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T04:56:02.249423Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.391899","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:09:51.494052Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.498463Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.499503Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.500468Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.502035Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.502948Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.503701Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.504061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.505065Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.505720Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.510648Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.511457Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.512144Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.512780Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.513364Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.513835Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.514214Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.514610Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.515013Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.515401Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.515745Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.516000Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.516628Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.517621Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.517964Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.519715Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.520616Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.522122Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.523074Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.523734Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.524387Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.525038Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.525642Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.526407Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:09:51.526890Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.108068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:25:46.114636Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.115193Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.115811Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.118895Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.119803Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.127048Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.127206Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.128785Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.129828Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131022Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131115Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131186Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131255Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131303Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131357Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131427Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131490Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131561Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131619Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131667Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131710Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131757Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131801Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131843Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131916Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.131960Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132025Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132133Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132215Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132268Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132316Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132359Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132403Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132451Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:25:46.132496Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.906882","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:26:16.928458Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.928663Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.928784Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.928926Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929024Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929231Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929420Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929528Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929596Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929675Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929771Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929824Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929886Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.929950Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930000Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930082Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930162Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930300Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930387Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930513Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930599Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930742Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.930905Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931009Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931067Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931122Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931184Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931266Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931341Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931520Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931692Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931846Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.931942Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.932017Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:26:16.932090Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.448345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:42:12.464690Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468321Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468512Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468594Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468663Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468740Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468809Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468880Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468936Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.468992Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469047Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469123Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469168Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469211Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469263Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469319Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469371Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469439Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469501Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469568Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469610Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469652Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469693Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469736Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469779Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469837Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469896Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469938Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.469981Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470023Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470067Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470109Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470153Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470196Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:42:12.470238Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.086649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:58:07.092802Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.093634Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.097668Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108290Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108498Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108632Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108730Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108811Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.108957Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.109066Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.109172Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.111895Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112307Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112398Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112467Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112545Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112620Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112700Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112766Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112819Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112879Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.112955Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113018Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113073Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113125Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113185Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113245Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113295Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113356Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113417Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113470Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113538Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113597Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113648Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:07.113761Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.682731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T05:58:37.697577Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.697800Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.697895Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.697992Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.698145Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.701161Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.701358Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.701529Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.701734Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.701916Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702012Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702074Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702146Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702203Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702341Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702457Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702535Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702625Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702698Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702756Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702813Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702868Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702921Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.702975Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703028Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703085Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703146Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703201Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703301Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703374Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703435Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703497Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703571Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703646Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T05:58:37.703717Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.051622","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:14:33.057258Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.062241Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.063854Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.064456Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.064638Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.064705Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.064796Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.064942Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068053Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068239Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068311Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068370Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068419Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068479Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068533Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.068600Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.073660Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.073810Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.073867Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.073913Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.073957Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074001Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074072Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074115Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074160Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074205Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074246Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074287Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074328Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074370Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074412Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074453Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074494Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074537Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:14:33.074580Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.537421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:30:28.560382Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.562560Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.562882Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563057Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563174Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563253Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563328Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563415Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563535Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563622Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563687Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563745Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563803Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563861Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.563984Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564162Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564235Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564331Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564428Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564603Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564707Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564831Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.564943Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565154Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565292Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565414Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565785Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.565953Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566170Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566394Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566579Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566706Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566852Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:28.566996Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.298798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:30:59.306414Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.306993Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.307105Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.307206Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310283Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310419Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310511Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310573Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310623Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310700Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310766Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310817Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310863Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310909Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.310955Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.311000Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.311062Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313495Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313597Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313671Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313744Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313795Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313851Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313905Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.313952Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314009Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314080Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314149Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314229Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314301Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314365Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314433Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314498Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314551Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:30:59.314625Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.863311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:42:15.872948Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.873574Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.891121Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.891503Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.892670Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895021Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895344Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895444Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895508Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895560Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895610Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895667Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895725Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895770Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895825Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895893Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.895961Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896026Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896084Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896131Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896174Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896219Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896262Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896306Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896349Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896393Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896436Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896479Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896533Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896576Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896619Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896662Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896705Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896779Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:42:15.896822Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.142766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:58:12.155285Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155517Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155580Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155647Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155714Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155794Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155907Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.155981Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.157841Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.158446Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.158607Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.159462Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.159737Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.160444Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.160681Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.161257Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.161422Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.161558Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.162405Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.163462Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.163841Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.164637Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.164798Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.164889Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.164949Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165007Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165095Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165146Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165198Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165249Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165300Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165356Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165432Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165497Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:12.165550Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.890883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T06:58:42.898896Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.899091Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.899185Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.900276Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.901301Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902527Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902649Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902724Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902783Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902849Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902908Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.902955Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903018Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903085Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903150Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903246Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903316Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903378Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903441Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903545Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903598Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903714Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903801Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903865Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903923Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.903982Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904036Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904090Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904140Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904192Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904281Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.904350Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.909480Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.909613Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T06:58:42.909670Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.919488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:10:53.925358Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.925837Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934230Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934414Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934490Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934552Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934618Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934720Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934857Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.934994Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935163Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935303Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935422Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935641Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935742Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935825Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935900Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.935975Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936041Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936124Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936242Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936301Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936346Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936400Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936449Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936511Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936617Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936695Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936759Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936807Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936866Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936920Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.936966Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.937164Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:10:53.937308Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.259345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:26:44.267177Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.267866Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.268674Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.269179Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.270209Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.276944Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.278101Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.279175Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.279459Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.279598Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.279703Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.279764Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.282163Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283020Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283170Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283252Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283311Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283366Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283417Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283467Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283512Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283556Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283620Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283671Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283715Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283764Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283813Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283856Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283899Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283943Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.283989Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.284031Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.284090Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.284135Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:26:44.284199Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.842470","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:27:14.858173Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858428Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858486Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858548Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858621Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858667Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858723Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858809Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858864Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858915Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.858969Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859022Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859092Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859139Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859189Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859274Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859319Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859366Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859416Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859467Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859519Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859572Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859618Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859667Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859715Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859779Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859835Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859902Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859943Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.859989Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.860066Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.860108Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.860149Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.860672Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:27:14.860801Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.349294","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:43:10.367050Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367280Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367455Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367558Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367626Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367685Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367749Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367844Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367904Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.367961Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368019Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368068Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368114Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368164Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368233Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368289Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368347Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368396Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368460Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368536Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368590Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368636Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368680Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368723Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368818Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.368931Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369015Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369085Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369141Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369202Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369262Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369314Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369363Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369421Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:43:10.369476Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.905689","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:59:05.911743Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.911995Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.912408Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.912547Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913142Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913293Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913396Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913466Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913525Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913588Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913638Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913693Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913743Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913804Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.913879Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.915963Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.916093Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919191Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919390Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919458Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919520Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919573Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919626Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919673Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919724Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919773Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919823Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919877Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919931Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.919981Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.920027Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.920073Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.920119Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.920166Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:05.920212Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.636582","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T07:59:36.643159Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.643752Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.644142Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.644298Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.644360Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.644559Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.644644Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646039Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646152Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646210Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646262Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646343Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646398Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646444Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646486Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646530Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.646573Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.651431Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.651573Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.651933Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.652021Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.653097Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.653658Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.654124Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.655124Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.656089Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.656319Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.656915Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.658051Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.658291Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.658428Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.659358Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.659502Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.659607Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T07:59:36.659700Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.804408","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:11:52.811864Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.812066Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.812141Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.812196Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813546Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813709Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813785Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813832Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813888Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813934Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.813979Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814025Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814077Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814137Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814420Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814510Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814574Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814631Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814687Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814732Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814775Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814821Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814864Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814907Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814950Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.814992Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815034Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815076Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815133Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815174Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815216Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.815265Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.821970Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.822100Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:11:52.822152Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.042613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:27:49.054901Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056276Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056472Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056733Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056819Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056879Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056932Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.056998Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057054Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057110Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057176Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057233Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057287Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057333Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057383Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057434Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057488Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057543Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057602Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057661Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057712Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057771Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057831Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057892Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057939Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.057993Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058050Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058101Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058184Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058228Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058285Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058347Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058445Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058535Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:27:49.058618Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.708242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:28:19.713445Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.720277Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.720571Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.720804Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.720949Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.721775Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.722536Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.722722Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.723173Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.723750Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.724389Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.724730Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725146Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725226Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725297Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725363Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725453Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725555Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725696Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725840Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.725897Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.726469Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.728452Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.728900Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729185Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729267Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729334Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729405Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729472Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729519Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729574Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.729624Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.731881Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.731995Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:28:19.732112Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.042540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:41:58.060374Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060596Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060663Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060723Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060799Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060869Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.060942Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.061018Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.061880Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.062197Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.062334Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063132Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063560Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063677Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063743Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063823Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063926Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.063983Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064048Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064130Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064189Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064234Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064278Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064321Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064366Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064424Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064478Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064521Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064564Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064607Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064650Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064693Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064737Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064782Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:41:58.064825Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:12.943117","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:45:15.823569Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.823864Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.824110Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.824530Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.824801Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.825106Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.825508Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.825839Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.826241Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.826485Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.826749Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.826849Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.827011Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.827398Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.827637Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.827820Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.828208Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.828470Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.828827Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.828972Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.829479Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.830084Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.830330Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.830750Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.831243Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.831989Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.832301Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.832460Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.832599Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.832894Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.833408Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.833968Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.835856Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.836026Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:15.836264Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.737805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:45:46.787850Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.788401Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.788474Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.788579Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.789014Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.789073Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.789575Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.789635Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790136Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790196Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790250Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790749Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790807Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790872Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.790940Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.791337Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792624Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792699Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792770Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792832Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792888Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.792957Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.793517Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794015Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794067Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794127Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794179Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794223Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794696Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794742Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794804Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794848Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.794928Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.795018Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:45:46.795654Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.690476","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:46:17.709226Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.709491Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.709659Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.709753Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710059Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710144Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710233Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710368Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710475Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710554Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710631Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710701Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710784Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710889Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.710965Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711050Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711135Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711208Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711279Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711367Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711446Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711507Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711642Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711723Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711777Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711832Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.711952Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.712007Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713068Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713145Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713229Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713361Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713418Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713463Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:17.713510Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.350339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:46:48.361073Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.361950Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.362086Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.362175Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.363010Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.364492Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.364919Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365277Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365500Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365569Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365619Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365665Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365711Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365755Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365800Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365844Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.365893Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368551Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368633Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368683Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368753Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368813Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368858Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368901Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368952Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.368996Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369040Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369082Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369125Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369168Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369211Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369465Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369511Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369556Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:46:48.369598Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.070345","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:47:19.077863Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078048Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078144Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078203Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078283Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078344Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078418Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078482Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078552Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078631Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078692Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.078776Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.079558Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.079659Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.079792Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.079853Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.079903Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082209Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082376Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082465Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082531Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082584Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082632Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082681Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082728Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082771Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082836Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082926Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.082988Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083036Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083095Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083191Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083247Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083309Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:19.083356Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.784293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:47:49.794787Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.796584Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.805222Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.806570Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.806773Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.807721Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.808580Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.808724Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809090Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809166Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809220Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809268Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809327Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809389Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809447Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809504Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809582Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809650Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809708Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809761Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809815Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809858Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809904Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809947Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.809993Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810040Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810083Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810125Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810175Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810217Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810259Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810299Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810340Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810383Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:47:49.810436Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.429960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:48:20.439830Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.440761Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.443908Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.444551Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.445014Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.447553Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448007Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448137Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448207Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448278Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448352Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448423Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448491Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448572Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448661Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448732Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448803Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448872Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448935Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.448983Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449030Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449084Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449160Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449229Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449281Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449326Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449454Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449570Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449625Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449669Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449712Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.449785Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.453443Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.453636Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:20.453730Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.241895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:48:51.249626Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.250273Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.250370Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.250698Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.250789Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.251278Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.252320Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.254328Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.256367Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.261080Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.261384Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.262508Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.262834Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.262923Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.262993Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263052Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263111Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263174Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263241Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263307Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263360Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263406Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263451Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263496Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263540Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263621Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263671Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263715Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263758Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263801Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263857Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263927Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.263978Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.264026Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:48:51.264072Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.198623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:49:22.205328Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.205604Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.206682Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.207157Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.207321Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.209687Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.210582Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.210883Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.210957Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211007Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211064Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211119Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211164Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211214Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211272Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211334Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.211404Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.214695Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.214957Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215036Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215159Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215258Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215318Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215368Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215415Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215461Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215506Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215559Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215638Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215765Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215843Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.215896Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.216123Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.216215Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:22.216282Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.325081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T08:49:53.333366Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.333597Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.334463Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.334670Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.335516Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.337313Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.338284Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.339312Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.340475Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.341405Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.341760Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.342445Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.345343Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.345442Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.345498Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.345545Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.345590Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.348557Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.348654Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.348959Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.349014Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.349056Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.350275Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351445Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351514Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351563Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351606Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351651Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351694Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351742Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.351785Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.352064Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.352106Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.352147Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T08:49:53.352187Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:28.749886","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:36:31.555673Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.559604Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.559947Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.560125Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.560310Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.560481Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.562115Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.562373Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566062Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566321Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566416Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566640Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566715Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566852Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.566932Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567005Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567080Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567165Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567231Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567295Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567362Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567434Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567532Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567613Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567700Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567785Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567873Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.567945Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568016Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568096Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568183Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568251Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568461Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568591Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:36:31.568671Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.772276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:37:02.786442Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.789206Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.792308Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.792660Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.793412Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.799745Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.799935Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800012Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800102Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800208Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800279Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800340Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800404Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800467Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800531Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800624Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800716Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800784Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800857Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800926Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.800981Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801030Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801075Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801120Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801175Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801279Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801344Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801393Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801437Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801542Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801604Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801665Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801712Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801771Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:02.801840Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.432177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:37:33.441163Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.444447Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.444616Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.444691Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.444761Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.444823Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445106Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445584Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445645Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445706Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445752Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.445810Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.446370Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.447101Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.448085Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.448178Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.448227Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.453765Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.453913Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.453985Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.455123Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.456343Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457118Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457222Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457436Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457509Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457558Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457603Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457816Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457860Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457902Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457944Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.457986Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.458028Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:37:33.458081Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.168586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:38:04.176880Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.176995Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.177165Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.178465Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.179750Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.180688Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.181715Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.182616Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.182875Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.183561Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.186668Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.188550Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190511Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190609Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190683Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190750Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190834Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190889Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.190951Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191010Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191061Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191103Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191145Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191187Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191238Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191288Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191341Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191384Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191425Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191466Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191524Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191565Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191607Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191649Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:38:04.191694Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:12.004121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:39:17.177263Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.177611Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.177976Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.178224Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.178590Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.178795Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179069Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179315Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179557Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179670Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179795Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.179862Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180022Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180100Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180345Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180426Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180480Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180545Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180595Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180643Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.180917Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.181234Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.181581Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.181701Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.181772Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.181850Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.182142Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.182237Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.182314Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.182490Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.183070Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.183321Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.183433Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.183486Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:17.183556Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.501186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:39:48.516671Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.517230Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.517481Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.517687Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.517820Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.517924Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518005Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518206Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518415Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518612Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518702Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518775Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.518988Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519375Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519469Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519568Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519657Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519807Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.519994Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520069Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520134Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520199Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520252Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520379Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520472Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520544Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520617Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.520853Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.521017Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.521239Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.521535Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.521620Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.531828Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.532169Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:39:48.532355Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.195927","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:40:19.202242Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.212307Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.212971Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213090Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213154Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213210Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213277Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213346Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213416Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.213478Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.214176Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.214978Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.216962Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217067Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217137Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217194Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217266Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217331Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217409Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217642Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217695Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217741Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217785Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217830Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217877Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217941Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.217994Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218061Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218104Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218146Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218396Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218451Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218515Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218563Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:19.218605Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.971809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:40:49.979646Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.981262Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.981720Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.983029Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.983158Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.983265Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.986142Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987089Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987187Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987241Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987292Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987352Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987405Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987476Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987550Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987602Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.987650Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990004Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990104Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990180Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990239Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990310Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990354Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990398Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990443Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990486Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990529Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990572Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990615Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990658Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990701Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990747Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990790Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990834Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:40:49.990878Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.748204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:41:20.758715Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.759177Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.759274Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.759343Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.759402Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.759450Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.760782Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761140Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761305Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761385Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761446Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761522Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761597Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761670Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761766Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761824Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761883Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.761953Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762040Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762142Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762253Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762314Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762367Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762461Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762518Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762583Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762663Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762733Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762790Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762836Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762885Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762936Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.762984Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.763030Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:20.763102Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.651514","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:41:51.661948Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.664287Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.665054Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.666937Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667056Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667119Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667188Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667253Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667344Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667409Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667463Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667518Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667578Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667650Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667709Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667762Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667823Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667892Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.667985Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668053Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668105Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668154Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668198Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668241Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668283Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668338Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668395Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668437Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668479Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668521Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668564Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668608Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668652Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668704Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:41:51.668752Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.392301","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:42:22.399446Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.400576Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.405233Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.405711Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.406013Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.406542Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.406659Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407484Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407573Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407651Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407721Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407792Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407858Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407916Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.407965Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.408021Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.409692Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.410670Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.411539Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.411667Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.411939Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.412669Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.412815Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.413527Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.413671Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.414529Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.414632Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.415504Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.415677Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.416716Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.418484Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.418570Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.422714Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.423482Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:22.423569Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.134106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:42:53.147195Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.150020Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.153422Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.154252Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.154369Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.154450Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.154531Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.156197Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.158106Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.158742Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.158906Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159085Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159222Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159304Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159423Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159561Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159661Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159757Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159855Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.159951Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160049Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160200Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160288Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160384Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160522Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160679Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160758Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160821Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160877Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160924Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.160971Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.161017Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.164569Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.164732Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:42:53.164801Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.459162","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:43:24.485097Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.485861Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.486320Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.486862Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.487458Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.488361Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.488821Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.489153Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.489438Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.489787Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.490081Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.490699Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.493922Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.495670Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.496738Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.497416Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.497661Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.498042Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.498489Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.499200Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.499372Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.499541Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.499907Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.500183Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.500331Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.500460Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.500794Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.501082Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.501962Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.502899Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.503302Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.503647Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.518100Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.518545Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:43:24.519823Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:51.046095","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:49:53.476968Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.483541Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.484702Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.485305Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.486916Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.511957Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.512456Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.521896Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.536136Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.542981Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.544173Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.544655Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.545079Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.545401Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.547390Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.548004Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.550923Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.552612Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.556885Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.558859Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.561142Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.567090Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.567520Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.567758Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.569035Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.569602Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.569820Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.569983Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.570190Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.570401Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.570821Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.571407Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.572055Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.572584Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:49:53.573092Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.029944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:50:24.045327Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.045544Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.045613Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.045676Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.045778Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.045895Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.046697Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.046899Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.046982Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.047067Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.047155Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.047342Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.047833Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.048108Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.048285Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.048835Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049122Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049315Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049501Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049618Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049693Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.049892Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050249Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050345Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050416Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050483Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050560Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.050735Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051036Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051237Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051509Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051636Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051748Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.051872Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:24.052101Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.830891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:50:54.843494Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.844179Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.845211Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846202Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846387Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846576Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846648Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846703Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846858Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.846955Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.847016Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.847102Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.849196Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850153Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850253Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850324Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850385Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850440Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850507Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850577Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850631Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850675Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850718Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850761Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850804Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850863Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850918Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.850961Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851004Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851074Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851135Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851284Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851340Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851450Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:50:54.851501Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.210613","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:51:25.221845Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.225726Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.226102Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.226163Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.226212Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229245Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229445Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229527Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229580Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229638Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229689Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229744Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229806Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229874Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.229936Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230008Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230069Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230129Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230210Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230263Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230307Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230349Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230395Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230475Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230526Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230601Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230667Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230711Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230761Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230888Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.230961Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.231006Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.231049Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.231095Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:25.231137Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.112707","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:51:57.163359Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.165295Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.166035Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.169856Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.170285Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.170733Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.170929Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.171156Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.171224Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.171799Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.171999Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.172697Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.172803Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.172868Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.172931Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.173083Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.173327Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.190761Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.190936Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191027Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191106Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191172Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191291Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191358Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191421Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191489Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191548Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191608Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191666Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191728Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191786Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191848Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191911Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.191973Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:51:57.192037Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.870421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:52:29.897937Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.898366Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.899942Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.902887Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.907080Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.909333Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.910443Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.910878Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.912735Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.913441Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.915373Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.916520Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.918246Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.918901Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.919495Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.920687Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.937601Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.937891Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.937981Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938045Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938097Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938147Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938246Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938332Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.938777Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.939421Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.940004Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.940561Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.940663Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.940855Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.941197Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.941352Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.941445Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.941593Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:52:29.942075Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.549607","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T19:53:00.561524Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.561706Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.561881Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.562189Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.562447Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.562578Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.562658Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.562817Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.564906Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.565580Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.565740Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.565873Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.565966Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566047Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566119Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566195Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566286Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566395Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566513Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566595Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566687Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566826Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.566957Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567074Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567168Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567245Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567316Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567382Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567436Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567481Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567525Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.567569Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.573381Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.573580Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T19:53:00.573743Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.855710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T20:01:38.866886Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.867247Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.867473Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.868633Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.869789Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.870640Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.872874Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.873651Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.874644Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876075Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876239Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876561Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876629Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876677Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876725Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876772Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.876830Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884577Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884714Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884771Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884840Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884925Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.884980Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.885596Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.885897Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.886844Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.887021Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.888583Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.888881Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.889871Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.890025Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.890655Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.890808Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.891560Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T20:01:38.891627Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.885477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T21:25:25.901346Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.901924Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.902675Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.903278Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.904157Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.904524Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.905237Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.905556Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906216Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906298Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906359Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906408Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906465Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906529Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906595Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906660Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906712Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906778Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906848Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906896Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.906939Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.909289Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910264Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910338Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910415Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910609Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910674Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.910887Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911012Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911277Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911417Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911547Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911692Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911770Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T21:25:25.911832Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.564857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:07:47.570469Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.572809Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579399Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579587Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579657Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579712Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579767Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579816Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579866Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579913Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.579961Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580009Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580073Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580142Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580218Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580384Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580465Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580538Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580604Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580674Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580746Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580805Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580860Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580907Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.580955Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581006Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581053Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581102Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581170Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581224Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581272Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581349Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581427Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581489Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:07:47.581542Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.388406","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:23:42.399085Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.399268Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.399396Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.399492Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.399637Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.399718Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.402687Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403123Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403264Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403382Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403516Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403639Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403784Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.403880Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404136Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404221Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404288Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404352Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404427Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404522Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404745Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404828Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.404955Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405059Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405167Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405284Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405351Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405447Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405529Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405604Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405680Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.405771Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.408490Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.408611Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:23:42.408672Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.050799","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:39:38.062999Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.063472Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.063923Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064039Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064142Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064259Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064363Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064622Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064740Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064843Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064934Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.064987Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065045Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065152Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065254Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065354Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065453Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065551Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065643Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065762Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065852Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.065933Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066008Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066057Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066105Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066189Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066277Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066358Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066504Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066593Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066667Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066752Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066901Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.066990Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:39:38.067071Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.752682","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:40:08.762064Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.762286Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.762398Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.762888Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.763092Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.764526Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.765101Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.766572Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.766752Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.766849Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.766955Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767042Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767101Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767157Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767211Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767274Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.767330Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770168Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770357Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770460Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770546Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770608Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770679Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770740Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770822Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.770924Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771041Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771101Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771160Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771233Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771297Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771367Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771447Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771559Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:40:08.771644Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.346283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T22:56:03.352843Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.354633Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.364723Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365563Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365685Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365750Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365813Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365869Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365917Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.365961Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366004Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366054Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366114Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366176Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366228Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366273Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366330Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366378Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.366425Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.368613Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.369393Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.369569Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.371606Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372549Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372615Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372661Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372702Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372743Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372783Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372824Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372867Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372913Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372954Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.372995Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T22:56:03.373035Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.068977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:11:58.075051Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.075414Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.076023Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.076774Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.076918Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077022Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077099Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077214Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077292Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077853Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.077983Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078085Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078179Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078320Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078401Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078470Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.078543Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.086797Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.088129Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.089978Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090174Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090377Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090512Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090593Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090659Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090717Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090770Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090839Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090898Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.090951Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.091005Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.091056Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.091107Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.091182Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:11:58.091237Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.980448","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:12:28.994252Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.994822Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.995398Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.995557Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.995748Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.997076Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.998014Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.998341Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.998424Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:28.998478Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.001855Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.003063Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.005069Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.006060Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.007053Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.008046Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.011116Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.013063Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.014040Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015042Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015159Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015249Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015332Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015418Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015486Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015547Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015616Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015754Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015836Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.015901Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.016207Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.016305Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.016379Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.016462Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:12:29.016534Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.437932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:28:24.450347Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.450580Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.451329Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.454263Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455137Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455262Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455475Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455630Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455749Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.455933Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.456002Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.456073Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.458763Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.458936Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459033Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459114Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459170Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459223Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459276Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459326Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459394Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459458Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459507Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459553Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459611Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459664Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459718Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459772Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459816Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459859Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459934Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.459980Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.460055Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.460112Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:28:24.460171Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.224273","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:44:20.232792Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.234141Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.234445Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.234940Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.235966Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.236619Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.236958Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.242166Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.244100Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.245765Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.246335Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.246571Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.246998Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.249120Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.250354Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.252477Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.253511Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.302991Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.305127Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.306756Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.308260Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.309206Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.312555Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.315061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.315968Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.316620Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.317134Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.317406Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.317937Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.319161Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.319502Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.319829Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.320131Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.321470Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:20.322524Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.357596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-21T23:44:51.367836Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.369712Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.369847Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.370301Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.372737Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.373729Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.375764Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376331Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376583Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376687Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376764Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376845Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376917Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.376981Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.377037Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.377087Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.377136Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380314Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380437Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380510Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380577Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380645Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380728Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380780Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380844Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380899Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.380946Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381033Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381147Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381219Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381293Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381388Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381461Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381521Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-21T23:44:51.381573Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
