{"timestamp":"2025-07-19T18:04:57.461550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:04:57.849307","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:00.912124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:01.310627","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:42.845426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:43.045204","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:19.672756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:19.733701","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:52.327079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:52.694280","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:17:40.961377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:41.200239","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:18:15.320941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:15.501706","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:16.022746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:16.097909","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:13.768360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.837432","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:45.090460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:45.144619","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:15.243387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:15.251939","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:49.215673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:49.711117","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:24.740000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:24.763326","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:54.980579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:54.988833","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:26.392833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:26.430472","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:02.851639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:03.090648","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:33.822265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:33.865521","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:04.342765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:04.359410","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:35.384898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:35.449674","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:13.853833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:13.909688","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:41:51.357204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:51.728034","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:42:28.493312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:28.512915","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:16.330087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:16.361805","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:46.929989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:46.941102","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:18.376895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:18.424994","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:48.777336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:48.795711","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:25.410403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:25.459327","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:55.625540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:55.633566","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:09:26.667068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:09:26.678449","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:28.884894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:28.976581","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:59.464404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:59.474045","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:29.663683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:29.682828","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:00.924997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:00.966680","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:04.321196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:04.361447","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:34.767187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:34.790660","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:05.384648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:05.394613","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:36.462561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:36.484466","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:06.937130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:06.946708","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:37.803276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:37.829242","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:08.758497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:08.777558","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:58.612128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:58.707319","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:29.282471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.301216","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:59.819269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.840525","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:17:32.541242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:32.564195","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:03.218935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.237124","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:34.185543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.192633","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:03.706921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:03.755794","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:34.220896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.240448","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:05.129313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:05.147929","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:36.015840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:36.030023","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:06.743650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.755733","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:37.530044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.543016","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:08.309938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.321994","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:39.044421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:39.058480","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:09.796490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.806664","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:40.431026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.441952","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:11.279777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.297893","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:41.988810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:41.998627","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:12.783243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.792007","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:43.414263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.423972","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:14.264581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.273878","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:45.016508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:45.026384","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:15.857008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.866443","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:46.665645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.674179","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:17.475155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.483619","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:48.325763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.334755","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:18.990087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:18.998672","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:49.757420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.768663","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:20.636222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.647419","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:51.405317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.415779","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:22.134463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.143003","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:52.917182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.924954","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:23.746831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.758424","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:54.544835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.555974","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:25.272756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.281921","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:56.139144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.148947","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:26.878580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.890486","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:57.837818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.849602","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:28.687577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.699458","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:59.415063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.425980","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:43:30.130554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.140292","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:00.833230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.843751","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:32.418137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.446222","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:02.709803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:02.738046","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:33.139009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:33.152270","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:03.491378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:03.504536","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:33.780559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:33.798791","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:04.754097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:04.764361","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:35.678171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:35.690398","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:06.536780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:06.545190","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:37.537653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:37.547868","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:08.453936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:08.463731","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:39.307733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:39.316427","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:10.127561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:10.138284","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:40.912688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:40.920763","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:11.814266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:11.826973","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:42.069981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:42.082868","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:13.219081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:13.231170","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:44.061009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:44.069356","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:14.962167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:14.975087","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:45.703653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:45.712978","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:16.465725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:16.477359","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:47.245220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:47.254708","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:18.316537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.335422","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:49.270467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.283873","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:20.382499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.390295","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:51.760108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.783174","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:22.461441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.471883","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:53.248383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.261029","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:23.984938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:23.993009","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:54.685533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.696215","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:25.920118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:25.935610","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:56.277984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.296965","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:27.094252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.103542","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:58.108997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.125793","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:01:29.055501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:29.068827","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:00.164217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.175135","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:31.590209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.626148","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:14.054543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:14.212223","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:44.727093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:44.750945","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:15.633403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:15.649053","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:46.654337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:46.669507","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:17.902637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:17.919935","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:48.312206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:48.355466","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:18.854947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:18.870685","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:49.960086","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:49.979905","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:12:20.451680","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:12:20.487400","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:12:51.255844","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:12:51.277450","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:13:21.920398","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:13:21.955072","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:13:52.167330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:13:52.185854","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:14:23.270298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:14:23.292014","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:14:53.816284","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:14:53.847958","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:15:24.021126","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:15:24.041191","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:15:34.888958","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:15:34.952572","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"SyntaxError","exc_value":"invalid syntax (02_generate_training_data_dag.py, line 40)","exc_notes":[],"syntax_error":{"offset":68,"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","line":"        shared_path = os.path.join(os.path.dirname(__file__), '..'.*$\n","lineno":40,"msg":"invalid syntax"},"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":995,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1133,"name":"get_code"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1063,"name":"source_to_code"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:06.586495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:06.631489","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"SyntaxError","exc_value":"invalid syntax (02_generate_training_data_dag.py, line 40)","exc_notes":[],"syntax_error":{"offset":68,"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","line":"        shared_path = os.path.join(os.path.dirname(__file__), '..'.*$\n","lineno":40,"msg":"invalid syntax"},"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":995,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1133,"name":"get_code"},{"filename":"<frozen importlib._bootstrap_external>","lineno":1063,"name":"source_to_code"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:17.245599","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:17.298780","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:16:44.553645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:16:44.687052","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:17:15.331708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:17:15.355379","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:17:46.189387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:17:46.275893","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:18:16.957149","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:18:17.071453","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:18:47.882235","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:18:47.955146","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:19:18.539260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:19:18.567757","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:19:48.911766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:19:48.932244","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:20:19.212373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:20:19.231506","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:20:49.673827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:20:49.691174","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:24:21.917598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:24:22.089116","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:24:53.137968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:24:53.150760","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:25:25.564954","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:25:25.605729","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:25:55.957777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:25:55.996374","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:26:26.638766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:26:26.684932","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:26:57.041546","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:26:57.062161","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:27:28.180593","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:27:28.204772","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:28:05.606093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:28:05.755974","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:28:35.984260","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:28:36.006732","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:06.931502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:06.992111","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:29:38.966568","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:29:39.036920","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:10.230459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:10.267081","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:30:40.593034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:30:40.622383","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:11.295760","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:11.313637","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:31:42.311468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:31:42.341085","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:32:13.534050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:32:13.554112","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:32:43.748079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:32:43.770251","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:33:14.654691","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:33:14.672238","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:34:52.246922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:34:52.350788","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:35:22.802817","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:35:22.819992","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:35:53.012293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:35:53.022904","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:36:23.179798","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:36:23.193461","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:36:54.182092","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:36:54.194623","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:37:25.116704","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:37:25.141064","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:37:55.972999","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:37:55.987647","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:38:26.890764","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:38:26.906040","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:38:58.869127","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:38:58.908623","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:39:29.828267","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:39:29.862918","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:40:00.294559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:40:00.310673","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:40:33.424034","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:40:33.468945","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:41:05.427285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:41:05.467608","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:41:35.664257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:41:35.686094","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:42:06.708822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:42:06.725628","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:42:37.731819","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:42:37.750635","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:43:09.267771","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:43:09.396860","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:43:44.636805","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:43:44.733656","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:44:14.835536","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:44:14.851203","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:46:27.192119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:46:27.622716","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:47:19.872619","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:47:20.663044","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:48:21.804569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:48:22.617620","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:48:57.129215","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:48:57.181712","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:49:28.354979","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:49:28.818177","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:50:05.263060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:50:05.701300","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:50:42.182001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:50:42.222247","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:51:13.317442","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:51:13.347164","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:51:44.535359","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:51:44.559826","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:52:14.946233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:52:14.963984","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:52:45.662837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:52:45.689292","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":49,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:52:48.127389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:52:48.210340Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:53:19.350013","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:53:19.372435Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:53:49.616165","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:53:49.631404Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:54:20.643745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:54:20.667708Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:54:51.181906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:54:51.199027Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:55:21.463577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:55:21.476525Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:58:24.600321","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:58:27.735358Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T21:00:50.122923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:00:51.100304Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T21:03:24.790177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T21:03:26.740905Z","level":"info","event":"Warning: Could not import shared modules: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.735267","level":"error","event":"Process timed out, PID: 262","logger":"airflow.utils.timeout.TimeoutPosix"}
{"timestamp":"2025-07-19T21:03:57.815797Z","level":"error","event":"Exception ignored in: <function _collection_gced at 0xffff7f184860>","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.827334Z","level":"error","event":"Traceback (most recent call last):","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.832360Z","level":"error","event":"  File \"/home/<USER>/.local/lib/python3.12/site-packages/sqlalchemy/event/registry.py\", line 53, in _collection_gced","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.833335Z","level":"error","event":"    def _collection_gced(ref):","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.837543Z","level":"error","event":"","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.842210Z","level":"error","event":"  File \"/home/<USER>/.local/lib/python3.12/site-packages/airflow/utils/timeout.py\", line 69, in handle_timeout","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.848763Z","level":"error","event":"    raise AirflowTaskTimeout(self.error_message)","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.850557Z","level":"error","event":"airflow.exceptions.AirflowTaskTimeout: DagBag import timeout for /opt/airflow/dags/02_generate_training_data_dag.py after 30.0s.","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.853987Z","level":"error","event":"Please take a look at these docs to improve your DAG import time:","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.858072Z","level":"error","event":"* https://airflow.apache.org/docs/apache-airflow/3.0.2/best-practices.html#top-level-python-code","chan":"stderr","logger":"processor"}
{"timestamp":"2025-07-19T21:03:57.861170Z","level":"error","event":"* https://airflow.apache.org/docs/apache-airflow/3.0.2/best-practices.html#reducing-dag-complexity, PID: 262","chan":"stderr","logger":"processor"}
