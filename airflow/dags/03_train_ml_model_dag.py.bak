from __future__ import annotations

import pendulum
import json
import os
import glob
import numpy as np
from airflow.decorators import dag, task
from airflow.models.param import Param
from airflow.datasets import Dataset

# Import shared modules
import sys
import os

# Add shared directory to Python path BEFORE importing shared modules
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.insert(0, shared_path)  # Use insert(0, ...) for higher priority
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.insert(0, shared_path)

# Now import shared modules
from shared.common import constants, validation_utils
from shared.common.model_management import save_fine_tuned_model, update_latest_model_pointer
from shared.common.definition_store import DefinitionStore

# Add Hugging Face imports
# These would be installed via requirements.txt
# transformers, datasets, torch, accelerate, peft

@dag(
    schedule=None,
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    tags=["llm-mapper", "ml-training", "huggingface", "model-management"],
    params={
        "service_names": Param("[]", type="string", description="JSON array of service names to include in training (empty for all)."),
        "training_data_path": Param("data/training_data", type="string", description="Path to the training data."),
        "model_type": Param("mapping", type="string", description="Type of model to train (mapping, classification, etc)."),
        "model_name": Param("llm-mapper-model", type="string", description="Name for the trained model."),
        "base_model": Param("google/flan-t5-small", type="string", description="Base model to fine-tune."),
        "model_output_path": Param("models/trained", type="string", description="Path to store the trained model."),
        "epochs": Param(3, type="integer", description="Number of training epochs."),
        "batch_size": Param(16, type="integer", description="Batch size for training."),
        "learning_rate": Param(2e-5, type="number", description="Learning rate for training."),
        "validation_split": Param(0.2, type="number", description="Fraction of data to use for validation."),
        "use_peft": Param(True, type="boolean", description="Whether to use PEFT for parameter-efficient fine-tuning."),
        "max_length": Param(512, type="integer", description="Maximum sequence length for tokenization."),
    },
    doc_md="""
    ### Train ML Model DAG with Hugging Face
    This DAG loads previously generated training data and fine-tunes a Hugging Face model for service mapping.
    
    - **Input**: Training data from the generate_training_data_dag
    - **Output**: Fine-tuned Hugging Face model and evaluation metrics
    """
)
def train_ml_model_dag():

    @task
    def load_training_data(service_names: list, training_data_path: str) -> dict:
        """Load training data for specified services or all available services"""
        all_examples = []
        metadata = {
            "services": [],
            "example_counts": {},
            "total_examples": 0
        }
        
        # If no specific services are requested, load all available
        if not service_names:
            # Find all service directories in the training data path
            service_dirs = [d for d in os.listdir(training_data_path) 
                           if os.path.isdir(os.path.join(training_data_path, d))]
            service_names = service_dirs
        
        # Load training data for each service
        for service_name in service_names:
            service_latest_path = f"{training_data_path}/{service_name}/latest"
            
            if not os.path.exists(service_latest_path):
                print(f"Warning: No training data found for service {service_name}")
                continue
            
            # Load training examples
            examples_path = f"{service_latest_path}/training_examples.json"
            if os.path.exists(examples_path):
                with open(examples_path, 'r') as f:
                    examples = json.load(f)
                    all_examples.extend(examples)
                    metadata["services"].append(service_name)
                    metadata["example_counts"][service_name] = len(examples)
                    metadata["total_examples"] += len(examples)
        
        if not all_examples:
            raise ValueError(f"No training examples found for the specified services: {service_names}")
        
        return {
            "examples": all_examples,
            "metadata": metadata
        }

    @task
    def format_for_huggingface(data: dict, model_type: str, validation_split: float) -> dict:
        """Format the data for Hugging Face training"""
        examples = data["examples"]
        metadata = data["metadata"]
        
        # Filter examples based on model type
        if model_type == "mapping":
            # For mapping models, focus on examples that map between WSDL and OpenAPI
            filtered_examples = [ex for ex in examples if ex["source"] == "mapping"]
        elif model_type == "classification":
            # For classification models, focus on operation classification
            filtered_examples = [ex for ex in examples if ex["source"] in ["wsdl", "openapi"]]
        elif model_type == "schema_mapping":
            # For schema mapping, focus on schema examples
            filtered_examples = [ex for ex in examples if "schema" in ex["source"]]
        else:
            # Default: use all examples
            filtered_examples = examples
        
        if not filtered_examples:
            raise ValueError(f"No suitable examples found for model type: {model_type}")
        
        # Format examples for sequence-to-sequence training
        formatted_examples = []
        
        for example in filtered_examples:
            if model_type == "mapping" and example["source"] == "mapping":
                # Format for WSDL to OpenAPI mapping
                input_text = (f"Map WSDL operation: {example['wsdl_operation']} "
                             f"from service: {example['service_name']}")
                
                output_text = (f"OpenAPI path: {example['openapi_path']} "
                              f"method: {example['openapi_method']} "
                              f"operationId: {example.get('openapi_operation_id', 'none')}")
                
                formatted_examples.append({
                    "input": input_text,
                    "output": output_text
                })
                
            elif model_type == "classification" and example["source"] in ["wsdl", "openapi"]:
                # Format for operation classification
                op_name = example.get("operation_name") or example.get("operation_id")
                if op_name:
                    input_text = f"Classify operation: {op_name} from service: {example['service_name']}"
                    output_text = f"Type: {example['features'].get('operation_type', 'unknown')}"
                    
                    formatted_examples.append({
                        "input": input_text,
                        "output": output_text
                    })
                    
            elif model_type == "schema_mapping" and "schema" in example["source"]:
                # Format for schema mapping
                if example["source"] == "wsdl_schema":
                    schema_name = example.get("type_name", "")
                    schema_structure = json.dumps(example.get("type_structure", {}))
                    
                    input_text = (f"Convert WSDL schema: {schema_name} "
                                 f"from service: {example['service_name']}")
                    
                    output_text = f"Schema structure: {schema_structure[:500]}"  # Truncate for practicality
                    
                    formatted_examples.append({
                        "input": input_text,
                        "output": output_text
                    })
        
        # Split into training and validation sets
        import random
        random.shuffle(formatted_examples)
        
        split_idx = int(len(formatted_examples) * (1 - validation_split))
        train_examples = formatted_examples[:split_idx]
        val_examples = formatted_examples[split_idx:]
        
        # Update metadata
        metadata["formatted_count"] = len(formatted_examples)
        metadata["train_count"] = len(train_examples)
        metadata["val_count"] = len(val_examples)
        metadata["model_type"] = model_type
        
        return {
            "train_examples": train_examples,
            "val_examples": val_examples,
            "metadata": metadata
        }

    @task
    def create_hf_datasets(formatted_data: dict, base_model: str, max_length: int) -> dict:
        """Create Hugging Face datasets from formatted examples"""
        # Import Hugging Face libraries
        from transformers import AutoTokenizer
        from datasets import Dataset
        
        train_examples = formatted_data["train_examples"]
        val_examples = formatted_data["val_examples"]
        metadata = formatted_data["metadata"]
        
        # Load tokenizer from the base model
        tokenizer = AutoTokenizer.from_pretrained(base_model)
        
        # Create datasets
        train_dataset = Dataset.from_dict({
            "input": [ex["input"] for ex in train_examples],
            "output": [ex["output"] for ex in train_examples]
        })
        
        val_dataset = Dataset.from_dict({
            "input": [ex["input"] for ex in val_examples],
            "output": [ex["output"] for ex in val_examples]
        })
        
        # Define tokenization function
        def preprocess_function(examples):
            inputs = examples["input"]
            targets = examples["output"]
            
            model_inputs = tokenizer(
                inputs, 
                max_length=max_length,
                padding="max_length",
                truncation=True
            )
            
            # Setup the tokenizer for targets
            with tokenizer.as_target_tokenizer():
                labels = tokenizer(
                    targets,
                    max_length=max_length,
                    padding="max_length",
                    truncation=True
                )
                
            model_inputs["labels"] = labels["input_ids"]
            return model_inputs
        
        # Apply preprocessing
        tokenized_train = train_dataset.map(
            preprocess_function,
            batched=True,
            remove_columns=train_dataset.column_names
        )
        
        tokenized_val = val_dataset.map(
            preprocess_function,
            batched=True,
            remove_columns=val_dataset.column_names
        )
        
        # Save tokenized datasets to disk for training
        timestamp = pendulum.now().timestamp()
        dataset_dir = f"data/hf_datasets/{metadata['model_type']}_{timestamp}"
        os.makedirs(dataset_dir, exist_ok=True)
        
        tokenized_train.save_to_disk(f"{dataset_dir}/train")
        tokenized_val.save_to_disk(f"{dataset_dir}/val")
        
        # Save tokenizer for later use
        tokenizer.save_pretrained(f"{dataset_dir}/tokenizer")
        
        return {
            "dataset_dir": dataset_dir,
            "train_size": len(tokenized_train),
            "val_size": len(tokenized_val),
            "metadata": metadata
        }

    @task
    def train_hf_model(dataset_info: dict, base_model: str, model_name: str, 
                      epochs: int, batch_size: int, learning_rate: float, 
                      use_peft: bool) -> dict:
        """Train a Hugging Face model using the prepared datasets"""
        # Import required libraries
        import torch
        from transformers import (
            AutoModelForSeq2SeqLM,
            AutoTokenizer,
            Seq2SeqTrainingArguments,
            Seq2SeqTrainer,
            DataCollatorForSeq2Seq
        )
        from datasets import load_from_disk
        
        # Load datasets
        dataset_dir = dataset_info["dataset_dir"]
        train_dataset = load_from_disk(f"{dataset_dir}/train")
        val_dataset = load_from_disk(f"{dataset_dir}/val")
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(f"{dataset_dir}/tokenizer")
        
        # Load base model
        model = AutoModelForSeq2SeqLM.from_pretrained(base_model)
        
        # Apply PEFT if requested
        if use_peft:
            from peft import LoraConfig, get_peft_model, TaskType
            
            # Configure LoRA
            peft_config = LoraConfig(
                task_type=TaskType.SEQ_2_SEQ_LM,
                r=8,  # rank
                lora_alpha=32,
                lora_dropout=0.1,
                target_modules=["q", "v"]  # Target attention layers
            )
            
            # Get PEFT model
            model = get_peft_model(model, peft_config)
            model.print_trainable_parameters()
        
        # Define training arguments
        training_args = Seq2SeqTrainingArguments(
            output_dir=f"{dataset_dir}/checkpoints",
            evaluation_strategy="epoch",
            save_strategy="epoch",
            learning_rate=learning_rate,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            weight_decay=0.01,
            save_total_limit=3,
            num_train_epochs=epochs,
            predict_with_generate=True,
            fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available
            report_to="none"  # Disable wandb, etc.
        )
        
        # Create data collator
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=tokenizer,
            model=model,
            padding="max_length"
        )
        
        # Initialize trainer
        trainer = Seq2SeqTrainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            tokenizer=tokenizer,
            data_collator=data_collator
        )
        
        # Train the model
        train_result = trainer.train()
        
        # Evaluate the model
        eval_result = trainer.evaluate()
        
        # Collect metrics
        metrics = {
            "train_loss": train_result.metrics["train_loss"],
            "eval_loss": eval_result["eval_loss"],
            "train_runtime": train_result.metrics["train_runtime"],
            "train_samples_per_second": train_result.metrics["train_samples_per_second"]
        }
        
        # Save the final model
        timestamp = pendulum.now().timestamp()
        model_dir = f"{dataset_dir}/final_model"
        
        # Save model and tokenizer
        trainer.save_model(model_dir)
        tokenizer.save_pretrained(model_dir)
        
        # Save training arguments and metrics
        with open(f"{model_dir}/training_args.json", "w") as f:
            json.dump(training_args.to_dict(), f, indent=2)
        
        with open(f"{model_dir}/metrics.json", "w") as f:
            json.dump(metrics, f, indent=2)
        
        return {
            "model_dir": model_dir,
            "dataset_dir": dataset_dir,
            "metrics": metrics,
            "model_type": dataset_info["metadata"]["model_type"],
            "model_name": model_name,
            "base_model": base_model,
            "use_peft": use_peft
        }

    @task
    def save_and_register_model(training_output: dict, model_output_path: str) -> dict:
        """Save the trained model to the model store and register it"""
        model_name = training_output["model_name"]
        model_type = training_output["model_type"]
        metrics = training_output["metrics"]
        model_dir = training_output["model_dir"]
        
        # Create version string with timestamp
        timestamp = pendulum.now().timestamp()
        version = f"v{timestamp}"
        
        # Create output directory
        output_dir = f"{model_output_path}/{model_name}/{version}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Copy model files to output directory
        import shutil
        for item in os.listdir(model_dir):
            s = os.path.join(model_dir, item)
            d = os.path.join(output_dir, item)
            if os.path.isdir(s):
                shutil.copytree(s, d, dirs_exist_ok=True)
            else:
                shutil.copy2(s, d)
        
        # Create model info file
        model_info = {
            "model_name": model_name,
            "model_type": model_type,
            "base_model": training_output["base_model"],
            "version": version,
            "timestamp": timestamp,
            "metrics": metrics,
            "use_peft": training_output["use_peft"],
            "model_path": output_dir
        }
        
        # Save model info
        with open(f"{output_dir}/model_info.json", "w") as f:
            json.dump(model_info, f, indent=2)
        
        # Update latest model pointer
        update_latest_model_pointer(model_name, version, f"{model_output_path}/model_pointers")
        
        # Create latest symlink/copy
        latest_dir = f"{model_output_path}/{model_name}/latest"
        if os.path.exists(latest_dir) and os.path.isdir(latest_dir):
            shutil.rmtree(latest_dir)
        
        # Copy files to latest
        shutil.copytree(output_dir, latest_dir)
        
        print(f"Model saved to {output_dir} and registered as latest version")
        
        return model_info

    # --- Task Execution Flow ---
    # Parse parameters
    service_names_json = "{{ params.service_names }}"
    service_names = json.loads(service_names_json) if service_names_json != "[]" else []
    
    training_data_path = "{{ params.training_data_path }}"
    model_type = "{{ params.model_type }}"
    model_name = "{{ params.model_name }}"
    base_model = "{{ params.base_model }}"
    model_output_path = "{{ params.model_output_path }}"
    
    epochs = int("{{ params.epochs }}")
    batch_size = int("{{ params.batch_size }}")
    learning_rate = float("{{ params.learning_rate }}")
    validation_split = float("{{ params.validation_split }}")
    use_peft = "{{ params.use_peft }}" == "True"
    max_length = int("{{ params.max_length }}")
    
    # Load training data
    data = load_training_data(service_names, training_data_path)
    
    # Format data for Hugging Face
    formatted_data = format_for_huggingface(data, model_type, validation_split)
    
    # Create Hugging Face datasets
    dataset_info = create_hf_datasets(formatted_data, base_model, max_length)
    
    # Train the model
    training_output = train_hf_model(
        dataset_info=dataset_info,
        base_model=base_model,
        model_name=model_name,
        epochs=epochs,
        batch_size=batch_size,
        learning_rate=learning_rate,
        use_peft=use_peft
    )
    
    # Save and register the model
    model_info = save_and_register_model(
        training_output=training_output,
        model_output_path=model_output_path
    )

# Instantiate the DAG
train_ml_model_dag_instance = train_ml_model_dag()