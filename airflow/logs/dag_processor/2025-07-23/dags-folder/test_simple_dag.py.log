{"timestamp":"2025-07-23T00:10:55.083329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:11:25.344545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:27:20.775206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:15.848588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:43:46.209944","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T00:59:41.689679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:00:12.448022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:16:06.977869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:02.041238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:32:32.795637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T01:48:27.663643","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:23.361040","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:04:54.374445","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:20:48.736648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:36:44.215487","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:37:15.103336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T02:53:09.739283","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:07:52.875845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:08:23.810373","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:24:19.326096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:14.717571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:40:45.507850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T03:56:39.905879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:12:35.734308","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:13:06.558920","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:29:02.069085","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:44:57.543757","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T04:45:28.477940","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:05.900462","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:04:37.042276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:20:30.959009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:21:02.116675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:36:56.652919","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:52:52.249464","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T05:53:22.700484","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:09:18.158079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:13.583520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:25:44.606977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:41:38.505495","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:49:33.064906","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T06:50:03.376802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:05:41.337667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:21:36.006486","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:22:06.925477","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:37:49.406054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:25.298645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T07:50:55.832441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:06:50.386907","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:07:21.211210","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:23:10.740236","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:06.707933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:39:37.517459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T08:51:41.757502","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:07:36.582002","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:08:07.505948","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:01.907102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:24:32.797736","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:40:27.490121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:52:39.308992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T09:53:10.459991","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:09:04.506634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:24:59.267558","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:25:30.210059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:41:24.340239","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:28.372054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T10:53:59.189922","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:09:53.754918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:20:50.161451","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:21:21.302616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T11:53:22.738447","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:16.785175","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:09:47.950923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:25:17.133776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:12.648730","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:41:43.651981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:54:38.908923","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T12:55:09.848942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:11:04.060216","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:14:53.679630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:21.705200","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:30:52.613372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:23.587050","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:31:54.533569","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:25.488516","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:32:58.008591","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:50:45.991960","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T13:51:16.919207","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:11:21.278714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:16.342237","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:27:47.211861","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:43:42.551313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:56:30.883884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T14:57:01.740846","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:12:56.541869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:28:52.091652","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:29:22.999282","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:17.662679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:45:48.796776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T15:57:55.447243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:13:38.346616","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:29:34.213571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:30:04.778973","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:45:58.983217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:58:31.696504","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T16:59:02.844548","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:14:57.453696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:30:40.968390","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:31:12.038291","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:47:05.784797","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T17:59:28.866638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:00:00.131681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:15:52.100404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:30:46.439876","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:31:17.150459","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:31:48.051667","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:32:18.781789","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:32:49.859538","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:33:20.785322","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:33:51.603265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:34:22.569272","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:34:53.384751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:35:24.012277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:35:55.116089","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:36:25.873794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:36:56.700365","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:37:27.605713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:37:58.439105","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:38:29.339277","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:39:00.179585","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:39:30.955298","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:40:02.021296","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:40:32.860508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:41:03.636793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:41:34.384828","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:42:05.165864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:42:36.080329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:43:06.871350","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:43:37.668800","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:44:08.523586","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:44:39.361545","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:45:10.158055","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:45:41.011850","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:46:11.863329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:46:42.665198","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:47:13.427376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:47:44.332004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:48:15.140712","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:48:45.928698","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:49:16.808498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:49:47.846539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:50:18.775685","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:50:49.596017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:51:20.409840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:51:51.292067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:52:22.180190","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:52:53.010874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:53:23.813571","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:53:54.698786","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:54:25.516407","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:54:56.369714","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:55:27.145953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:55:58.026059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-23T18:56:28.800461","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_simple_dag.py","logger":"airflow.models.dagbag.DagBag"}
